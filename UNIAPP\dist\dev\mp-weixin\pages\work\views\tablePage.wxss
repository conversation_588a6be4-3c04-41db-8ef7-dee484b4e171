/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.pageBox.data-v-df564ecf {
  overflow: auto !important;
}
.tableBox.data-v-df564ecf {
  width: 100%;
  min-height: 80vh;
  border-top: 1px solid #EEF1FE;
  border-bottom: 1px solid #EEF1FE;
  padding: 0 20rpx;
  font-size: 12px;
  color: #666666;
}
.tableTitle.data-v-df564ecf {
  padding: 30rpx 15rpx;
  display: flex;
  justify-content: space-between;
  font-size: 27rpx !important;
}
.tableContent.data-v-df564ecf {
  width: 100%;
}
.tableItem.data-v-df564ecf {
  display: flex;
}
.tableItem.data-v-df564ecf:nth-child(odd) {
  background-color: #F8F8FF;
}
.synthesize.data-v-df564ecf {
  display: flex;
  font-size: 12px;
  align-items: center;
  justify-content: center;
  background-color: #F8F8FF;
}
.synthesize-left.data-v-df564ecf {
  flex: 2;
  width: 170rpx;
  padding: 8px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #EEF1FE;
  box-sizing: border-box;
  border-bottom: 1px solid transparent;
  border-right: 1px solid transparent;
}
.synthesize-middle.data-v-df564ecf {
  flex: 7;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  max-width: 486rpx;
  border-top: 1px solid #EEF1FE;
  border-left: 1px solid #EEF1FE;
  padding: 9px 4px 8px;
  text-align: center;
}
.synthesize-right.data-v-df564ecf {
  flex: 1;
  width: 120rpx;
  text-align: center;
  padding: 8px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #EEF1FE;
  box-sizing: border-box;
  border-bottom: 1px solid transparent;
}
.self.data-v-df564ecf {
  display: flex;
}
.self-left.data-v-df564ecf {
  flex: 2;
  max-width: 152rpx;
  padding: 8px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #EEF1FE;
  box-sizing: border-box;
  border-right: 1px solid transparent;
}
.self-right.data-v-df564ecf {
  width: 610rpx;
  flex: 8;
  border: 1px solid #EEF1FE;
}
.cell1.data-v-df564ecf {
  flex: 2;
  width: 170rpx;
  padding: 8px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #EEF1FE;
  box-sizing: border-box;
  border-bottom: 1px solid transparent;
  border-right: 1px solid transparent;
}
.cellBox.data-v-df564ecf {
  flex: 7;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.first.data-v-df564ecf {
  text-align: center;
}
.cell2.data-v-df564ecf {
  flex: 5;
  width: 352rpx;
  padding: 8px 4px;
  border: 1px solid #EEF1FE;
  box-sizing: border-box;
  border-bottom: 1px solid transparent;
  border-right: 1px solid transparent;
  text-indent: 2px;
}
.cell3.data-v-df564ecf {
  flex: 2;
  width: 124rpx;
  text-align: center;
  padding: 8px 4px;
  border: 1px solid #EEF1FE;
  box-sizing: border-box;
  border-bottom: 1px solid transparent;
  border-right: 1px solid transparent;
}
.cell4.data-v-df564ecf {
  flex: 1;
  width: 120rpx;
  text-align: center;
  padding: 8px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #EEF1FE;
  box-sizing: border-box;
  border-bottom: 1px solid transparent;
}
.cellContent.data-v-df564ecf {
  width: 100%;
  display: flex;
  align-items: stretch;
  box-sizing: border-box;
}
.tableHeader.data-v-df564ecf {
  background-color: #F8F8FF;
}
.textarea.data-v-df564ecf {
  width: 100%;
  background-color: white;
  border-radius: 5px;
  border: none;
  color: #666666;
  letter-spacing: 1rpx;
  text-align: left;
  font-size: 25rpx;
}
.textarea.data-v-df564ecf:before {
  content: attr(data-currentCount);
  position: absolute;
  right: 90rpx;
  bottom: 5px;
  color: #ccc;
}
.textarea.data-v-df564ecf::after {
  content: attr(data-maxLength);
  position: absolute;
  right: 5px;
  bottom: 5px;
  color: #666666;
}