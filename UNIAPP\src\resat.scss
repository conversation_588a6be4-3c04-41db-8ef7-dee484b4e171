/* 自定义css */
page {
  --size-9: 48rpx;
  --size-8: 45rpx;
  --size-7: 42rpx;
  --size-6: 38rpx;
  --size-5: 36rpx;
  --size-4: 34rpx;
  --size-3: 30rpx;
  --size-2: 28rpx;
  --size-1: 24rpx;
  --mask-1: rgba(0, 0, 0, 0.4);
  --mask-2: rgba(0, 0, 0, 0.6);
  --mask-3: rgba(0, 0, 0, 0.8);

  // 还有uview的变量 使用 $primary
  /* default */
  --bg-home: #ebebf0;
  /* 状态面板背景 */
  --background-status-panel: radial-gradient(circle,
      rgba(5, 2, 47, 1) 28%,
      rgba(55, 41, 152, 1) 86%,
      rgba(54, 40, 151, 1) 110%);
  --card-border: transition;
  /* 登录面板背景 */
  --background-login: linear-gradient(80deg, #677BF0, #5C6EED);
  --card-title-bg: white;
  --bg-aside: #242424;
  --primary-color: #4bd9cc;
  --primary: #4bd9cc;

  --bk: rgb(0, 0, 0);
  --gray: #2c2c3a;
  --white: white;
  --black: rgb(50, 50, 50);
  --bg-head: white;
  --text-shadow: 0 0 4px #ccc;
  --box-shadow: 0 0 10px #ccc;
  --card-head-bg: #F2F2F7;
  --card-body-bg: white;
  --card-footer-bg: white;
  --error-bg: #ffe9ee;
  --text-color: rgba(22, 6, 48, 0.86);
  /* color -light - ui*/
  --warn: #fbbd08;
  --success: #39b54a;
  --success-light: #8dc63f;
  --info: #8799a3;
  --blue: #0081ff;
  --ocean: #02b6d2;
  --blue-deep: #5272F7;
  /** 天青 */
  --cyan: #1cbbb4;
  --danger: #f56c6c;
  --warning: #f37b1d;
  --warm: #f5a623;
  --pink: #F9D7EA;
  --pink-deep: #E03997;
  --line: #d5e1e7d1;


  --success-light: #7ef68e54;
  --blue-light: #65e6ff5c;
  --blue-text: #4190dd;
  --danger-light: #5a1e32;
  --danger-text: #f56c6c;
  /** 几乎纯白 */
  --info-light: #EEF1FE;
  --info-text-light: #3E4A79;
  --info-text: #666666;
  --warn-light: #fff2be66;

  --common-text: #212e63;
  --dialog-bg: #ffffff;
  --dialog-head-bg: #F2F2F7;

  --lineLight: #b8b6b6;
  --wx-danger: #CC5353;
}

.delHead{
  height: calc(100vh - 13vh);
    display: flex;
    flex-direction: column;
    padding: 20rpx;
    margin-top: 12vh;
    
}
// form 表单提示
.u-form-item__body__right__message {
  margin-left: 0px !important;
  text-indent: 11vw !important;
}

// 被评人
.eval-form .u-form-item__body__left__content__label {
  min-width: 124rpx !important;
    
}
// 被评人
// .time-form .u-form-item__body__left__content__label {
//   min-width: 224rpx !important;
//   color: #dd2e2e;
    
// }

.pageBox {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100vh;
  background-color: white;
  font-family: 'Helvetica Neue, Helvetica, Arial, PingFang SC, Microsoft YaHei, Myriad pro, Droid Sans';
  /* border: 1px solid ; */
}

/* 头部 */
.head {
  height: 13vh;
  color: black;
  font-size: 37rpx;
  color: rgba(80, 80, 80, 1);
  display: flex;
  align-items: center;
  padding-top: 25rpx;
  position: relative;
}

.body {
  height: 84vh;
  width: 100vw;
}

/* 所有也头部返回按钮,右边也padding 60像素是为了方便点击 */
.return {
  position: absolute;
  padding: 20rpx 60rpx 0rpx 40rpx;
}

.return-img {
  width: 24rpx;
  height: 40rpx;
}

/* 所有页面的头部标题 */
.title {
  margin: 0 auto;
}

.star {
  color: crimson;
  margin-right: 5rpx;
  font-size: 1.5rem;
}

.icon {
  width: 50rpx;
  height: 40rpx;
}

.overText {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.hide {
  opacity: 0%;
}

.none {
  display: none !important;
}

.flex {
  display: flex;
  justify-content: center;
  place-items: center;
}

.bd {
  border: 1px solid black !important;
}

.bug {
  height: 100rpx;
}

// icon
.uicon-close {
  font-size: 16px !important;
  margin-right: 0px;
  position: relative;
  color: transparent !important;
}

.uicon-close::before {
  content: '✖';
  position: absolute;
  display: flex;
  text-align: right;
  place-items: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  color: #f7f7f7 !important;
  // background-color: black;
}

.u-cell__body {
  padding: 40rpx !important;
  font-size: 34rpx !important;
  letter-spacing: 2px;
}