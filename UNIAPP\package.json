{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4000720240327002", "@dcloudio/uni-app-plus": "3.0.0-4000720240327002", "@dcloudio/uni-components": "3.0.0-4000720240327002", "@dcloudio/uni-h5": "3.0.0-4000720240327002", "@dcloudio/uni-mp-alipay": "3.0.0-4000720240327002", "@dcloudio/uni-mp-baidu": "3.0.0-4000720240327002", "@dcloudio/uni-mp-jd": "3.0.0-4000720240327002", "@dcloudio/uni-mp-kuaishou": "3.0.0-4000720240327002", "@dcloudio/uni-mp-lark": "3.0.0-4000720240327002", "@dcloudio/uni-mp-qq": "3.0.0-4000720240327002", "@dcloudio/uni-mp-toutiao": "3.0.0-4000720240327002", "@dcloudio/uni-mp-weixin": "3.0.0-4000720240327002", "@dcloudio/uni-mp-xhs": "3.0.0-4000720240327002", "@dcloudio/uni-quickapp-webview": "3.0.0-4000720240327002", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "pinia": "^2.0.33", "pinia-plugin-persistedstate": "^3.2.3", "uview-plus": "^3.3.4", "vue": "^3.2.31"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "3.0.0-4000720240327002", "@dcloudio/uni-cli-shared": "3.0.0-4000720240327002", "@dcloudio/uni-stacktracey": "3.0.0-4000720240327002", "@dcloudio/vite-plugin-uni": "3.0.0-4000720240327002", "@types/lodash": "^4.17.16", "@vue/runtime-core": "^3.3.11", "@vue/tsconfig": "^0.1.3", "sass": "^1.77.7", "sass-loader": "^10.5.2", "typescript": "^4.9.4", "vite": "4.3.5", "vue-tsc": "^1.0.24"}}