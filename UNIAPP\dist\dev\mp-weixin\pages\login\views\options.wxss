/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.text.data-v-4e8b9ee0 {
  margin: 20rpx 20rpx 40rpx 20rpx;
  width: -moz-fit-content;
  width: fit-content;
  height: auto;
  font-family: PingFang SC;
  font-size: 40rpx;
  color: #383A49;
  line-height: 73rpx;
  text-indent: 40rpx;
}
.btn_box.data-v-4e8b9ee0 {
  width: 100%;
  height: -moz-fit-content;
  height: fit-content;
  padding-top: 40rpx;
  display: flex;
  justify-content: space-around;
}
.button1.data-v-4e8b9ee0 {
  width: 300rpx;
  height: 84rpx;
  background: #E6E6E6;
  box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0, 0, 0, 0.1);
  border-radius: 0rpx 42rpx 0rpx 42rpx;
}
.button2.data-v-4e8b9ee0 {
  width: 300rpx;
  height: 84rpx;
  background: linear-gradient(80deg, #677BF0, #2330DF);
  box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0, 0, 0, 0.2);
  border-radius: 42rpx 0rpx 42rpx 0rpx;
  color: white;
}