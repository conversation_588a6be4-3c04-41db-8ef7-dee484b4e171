<template>
	<view class="pageBox">
		<view class="head">
		    <view class="return" @click="returnSetp"> 
				<image class="return-img" src="../../../../static/icons/fanhui-white.png" ></image>
			</view>
		    <text class="title">账号申诉</text>
		</view>
		<view class="stepBox">
			<view class="padding" style="background-color: transparent;">
				<view class="cu-steps">
					<view class="cu-item" :class="index>step?'':'text-blue'" v-for="(item,index) in numList" :key="index">
						<text class="num bg-white" :class="index==2?step_status:''" :data-index="index + 1"></text> {{item.name}}
					</view>
				</view>
			</view>
		</view>
		<!-- 上传身份证 -->
		<view class="uploadBox" :class="step=== 0?'':'none'">
			<view>
				<image class="IDCard-image" src="../../../../static/icons/card.png" mode="aspectFit"></image>
			</view>
			<view class="uploadBox_text" @click="toUpload()">
				<text style="font-size: var(--size-4);font-weight: 500;">上传身份证等材料</text>
				<view style="color: #B3B3B3;letter-spacing: 0px;">证明是该账户所有者</view>
			</view>
			<view>
				<image class="right-image" src="../../../../static/mine/right.png" mode="aspectFit"></image>
			</view>
		</view>
		<!-- 审核中 -->
		<view class="checking" :class="step===1?'':'none'">
			<image class="ball" src="../../../../static/icons/ball.png" mode="aspectFill"></image>
			<view style="color: var(--blue-2);font-size: var(--size-5);text-indent: 40rpx;">正在申诉中...</view>
			<view class="margin-top-sm" style="width: 70%;margin-left: 15%;color: #8799a3;">预计3个工作日内审核完毕，审核结果 会短信通知到您的手机号上</view>
		</view>
		<!-- 审核结果 -->
		<view class="checked" :class="step===2?'':'none'">
			<view>
				<image class="IDCard-image" src="../../../../static/icons/uploadFile.png" mode="aspectFit"></image>
			</view>
			<view class="uploadBox_text" @click="toUpload()">
				<text style="font-size: var(--size-4);font-weight: 500;">{{check_result}}</text>
				<view style="color: #B3B3B3;letter-spacing: 0px;">{{check_text}}</view>
			</view>
			<view>
				<image class="right-image" src="../../../../static/mine/right.png" mode="aspectFit"></image>
			</view>
		</view>
		
	</view>
</template>

<script lang="ts" setup>
	// -> 导入-------------------

import { ref } from 'vue';

	
	// -> 数据-------------------
	const step = ref(2)
	const numList = ref([{
		name: '上传证明资料'
	},{
		name: '客服审核'
	},{
		name: '完成'
	}])
	let step_status = ref('') //'' || err'
	let check_result = ref('申诉成功')
	let check_text = ref('设置新密码登录')
	// -> 函数-------------------
	const returnSetp = ()=>{
		uni.navigateBack({
			delta: 1
		})
	}
	const toUpload = () =>{
		uni.navigateTo({
			url:'/pages/login/views/acount/uploadIDCard'
		})
		// autoIncrement()
	}
	const autoIncrement = () =>{
		if(step.value === 2){
			step.value = -1
			return
		}
		step.value += 1
	}
</script>

<style scoped lang="scss">
	.head{
		background: linear-gradient(100deg, #677BF0, #2330DF);
		box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0,0,0,0.2);
		color: white;
	}
	.stepBox{
		width: 100%;
		height: 200rpx;
		background: linear-gradient(80deg, #677BF0, #2330DF);
		box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0,0,0,0.2);
	}
	.cu-item{
		// border: 2px solid white;
	}
	// ⚪
	.num{
		margin-top: 10rpx !important;
		border: 2px solid #2330DF !important;
		padding: 5px;
		line-height: 30px !important;
		font-size: 1.1rem !important;
	}
	.cu-steps .cu-item[class*="text-"] .num::after{
		background-color: #2330DF;
	}
	.uploadBox{
		margin: 60rpx auto;
		width: 90%;
		padding: 40rpx;
		box-shadow: 0 0 4px #ccc;
		display: flex;
		border-radius: 10px;
	}
	.uploadBox>view{
		width: 20%;
		height: 80rpx !important;
		height: fit-content;
		flex: 1;
		// border: 1px solid ;
	}
	.IDCard-image{
		width: 100%;
		height: 100%;
	}
	.uploadBox_text{
		flex: 4 !important;
		text-indent: 20rpx;
		line-height: 42rpx;
		letter-spacing: 1rpx;
	}
	.right-image{
		margin-left: 50rpx;
		margin-top: 22rpx;
		width: 45rpx;
		height: 40rpx;
	}
	
	
	.checking{
		margin-top: 20rpx;
		width: 100%;
		height: auto;
		padding: 20rpx 0px;
		text-align: center;
	}
	.ball{
		width: 80%;
		max-height: 300rpx;
	}
	
	.checked{
		margin: 60rpx auto;
		width: 90%;
		padding: 40rpx;
		box-shadow: 0 0 4px #ccc;
		display: flex;
		border-radius: 10px;
		
	}
	.checked>view{
		width: 20%;
		height: 80rpx !important;
		height: fit-content;
		flex: 1;
		// border: 1px solid ;
	}
	.IDCard-image{
		width: 100%;
		height: 100%;
	}
	.uploadBox_text{
		flex: 4 !important;
		text-indent: 20rpx;
		line-height: 42rpx;
		letter-spacing: 1rpx;
	}
	.right-image{
		margin-left: 50rpx;
		margin-top: 22rpx;
		width: 45rpx;
		height: 40rpx;
	}
	
</style>