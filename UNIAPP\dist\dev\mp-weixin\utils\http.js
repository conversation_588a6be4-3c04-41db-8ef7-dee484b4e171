"use strict";
const utils_request = require("./request.js");
const utils_index = require("./index.js");
const login = async (data) => {
  const parm = {
    mobile: data.mobile,
    password: utils_index.generateHash(data.password)
    // 确保密码加密后再发送
  };
  return await utils_request.request({ url: "/Http2/Login", data: parm });
};
const getOpenidAPI = async (data) => {
  return await utils_request.request({ url: "/WxOpen/GetOpenId", data });
};
const refreshToken = async (data) => {
  return await utils_request.request({ url: "/Http2/LoginByOpenId", data });
};
const getIsDepartmentlanager = async () => {
  return await utils_request.request({ url: "/Http2/IsDepartmentManager" });
};
const getTransDepartment = async (data) => {
  return await utils_request.request({ url: "/Http2/ApplyCrossDepartmentEvaluation", data });
};
const getAssign = async (data) => {
  return await utils_request.request({ url: "/Http2/AssignCrossDepartmentEvaluator", data });
};
const getSupervisorList = async () => {
  return await utils_request.request({
    url: "/Http2/GetCrossDepartmentApprovalRequests"
  });
};
const getApplyforList = async () => {
  return await utils_request.request({
    url: "/Http2/GetMyAppliedCrossDepartmentRequests"
  });
};
const getApplyforListRequest = async (data) => {
  return await utils_request.request({
    url: "/Http2/ApproveCrossDepartmentRequest",
    data
  });
};
const getCheckRationality = async () => {
  return await utils_request.request({
    url: "/Http2/checkRationality"
  });
};
const getDeadline = async () => {
  return await utils_request.request({
    url: "/Http2/GetDeadline"
  });
};
const getUpdateDeadline = async (data) => {
  return await utils_request.request({
    url: "/Http2/UpdateDeadline",
    data
  });
};
const getAppraisalStatistics = async (data) => {
  return await utils_request.request({ url: "/Http2/GetAppraisalStatistics", data });
};
const getSelfNotice = async () => {
  return await utils_request.request({ url: "/Http2/GetUsersNotSelfEvaluated" });
};
const getMutualNotice = async () => {
  return await utils_request.request({ url: "/Http2/GetUsersNotMutuallyEvaluated" });
};
const getToptoBottom = async () => {
  return await utils_request.request({
    url: "/Http2/GetSupervisorsNotEvaluatedSubordinates"
  });
};
const getBottomtoTop = async () => {
  return await utils_request.request({
    url: "/Http2/GetSubordinatesNotEvaluatedSupervisors"
  });
};
const getQuestions = async (data) => {
  return await utils_request.request({ url: "/Http2/GetEvaluation", data });
};
const getDepartmentInfo = async (data) => {
  return await utils_request.request({ url: "/Http2/GetDepartList", data });
};
const setAppraisal = async (data) => {
  return await utils_request.request({ url: "/Http2/SetAppraisal", data });
};
const getHistoryList = async (data) => {
  return await utils_request.request({ url: "/Http2/GetTimeline", data });
};
const getAHistoryItem = async (data) => {
  return await utils_request.request({ url: "/Http2/GetAppraisal", data });
};
const getUserInfo = async () => {
  return await utils_request.request({ url: "/User/GetUser" });
};
const setUserEmail = async (data) => {
  return await utils_request.request({ url: "/User/SetEmail", data });
};
const sendSms = async (data) => {
  return await utils_request.request({ url: "/Http2/SendSms", data });
};
const updatePhone = async (data) => {
  return await utils_request.request({ url: "/User/UpdateMobile", data });
};
const updatePassword = async (data) => {
  return await utils_request.request({ url: "/User/SetPwd", data });
};
const findPassword = async (data) => {
  return await utils_request.request({ url: "/Http2/RetrievePassword", data });
};
const getLogout = async (data) => {
  return await utils_request.request({ url: "/Http2/Logout" });
};
exports.findPassword = findPassword;
exports.getAHistoryItem = getAHistoryItem;
exports.getApplyforList = getApplyforList;
exports.getApplyforListRequest = getApplyforListRequest;
exports.getAppraisalStatistics = getAppraisalStatistics;
exports.getAssign = getAssign;
exports.getBottomtoTop = getBottomtoTop;
exports.getCheckRationality = getCheckRationality;
exports.getDeadline = getDeadline;
exports.getDepartmentInfo = getDepartmentInfo;
exports.getHistoryList = getHistoryList;
exports.getIsDepartmentlanager = getIsDepartmentlanager;
exports.getLogout = getLogout;
exports.getMutualNotice = getMutualNotice;
exports.getOpenidAPI = getOpenidAPI;
exports.getQuestions = getQuestions;
exports.getSelfNotice = getSelfNotice;
exports.getSupervisorList = getSupervisorList;
exports.getToptoBottom = getToptoBottom;
exports.getTransDepartment = getTransDepartment;
exports.getUpdateDeadline = getUpdateDeadline;
exports.getUserInfo = getUserInfo;
exports.login = login;
exports.refreshToken = refreshToken;
exports.sendSms = sendSms;
exports.setAppraisal = setAppraisal;
exports.setUserEmail = setUserEmail;
exports.updatePassword = updatePassword;
exports.updatePhone = updatePhone;
