"use strict";
const common_vendor = require("../common/vendor.js");
if (!Math) {
  NoData();
}
const NoData = () => "./noData.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "myTable",
  props: {
    columns: {},
    data: {}
  },
  setup(__props) {
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: _ctx.data.length
      }, _ctx.data.length ? {
        b: common_vendor.f(_ctx.columns, (column, index, i0) => {
          return {
            a: common_vendor.t(column.title),
            b: index,
            c: column.width || 1
          };
        }),
        c: common_vendor.f(_ctx.data, (row, rowIndex, i0) => {
          return {
            a: common_vendor.f(_ctx.columns, (column, colIndex, i1) => {
              return {
                a: common_vendor.t(row[column.prop]),
                b: colIndex,
                c: column.width || 1
              };
            }),
            b: rowIndex,
            c: rowIndex % 2 === 1 ? 1 : ""
          };
        })
      } : {}, {
        d: common_vendor.p({
          data: _ctx.data
        })
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-608e08cb"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/components/myTable.vue"]]);
wx.createComponent(Component);
