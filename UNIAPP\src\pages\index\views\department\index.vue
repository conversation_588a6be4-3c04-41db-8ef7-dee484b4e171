<template>
  <view>
    <Head @onReturn="FuReturn" :title="title" returnType="home"></Head>
    <BasicEvaluation v-if="TIdNum <= 3" />
    <CrossEvaluation v-else />
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useIndexStore } from "@/store";
import Head from "@/components/head.vue";
import BasicEvaluation from "./BasicEvaluation/index.vue";
import CrossEvaluation from "./CrossEvaluation/index.vue";
const emit = defineEmits(["onReturn"]);
const store = useIndexStore();
const TIdNum = ref<number>(store.submitData.TId);

defineProps({
  title: {
    type: String,
    default: "选择部门",
  },
});
const FuReturn = () => {
  emit("onReturn");
};
</script>

<!-- department/
├── BasicEvaluation/
│ ├── index.vue # 基础评测组件（0-3）
│ └── components/
│ └── UserList.vue # 用户列表组件
│
├── CrossEvaluation/
│ ├── index.vue # 跨部门评测组件（4-5）
│ └── components/
│ ├── EvalForm.vue # 评测表单组件
│ └── UserList.vue # 用户列表组件
│
└── index.vue # 主入口，根据 TIdNum 渲染不同模块 -->
