"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const store_user = require("../../../store/user.js");
require("../../../utils/request.js");
require("../../../utils/index.js");
require("../../../store/pinia.js");
require("../../../utils/http.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "receiveComments",
  setup(__props) {
    const store = store_index.useIndexStore();
    store_user.useUserStore();
    const ref_toast = common_vendor.ref();
    console.log(ref_toast.value);
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => common_vendor.unref(store).backAPage())
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/mine/views/receiveComments.vue"]]);
wx.createPage(MiniProgramPage);
