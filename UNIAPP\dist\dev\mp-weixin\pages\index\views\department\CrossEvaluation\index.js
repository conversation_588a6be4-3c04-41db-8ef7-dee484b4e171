"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const store_user = require("../../../../../store/user.js");
const store_index = require("../../../../../store/index.js");
const utils_http = require("../../../../../utils/http.js");
require("../../../../../utils/request.js");
require("../../../../../utils/index.js");
require("../../../../../store/pinia.js");
require("../../../../../utils/sign.js");
require("../../../../../utils/sha1.js");
if (!Math) {
  (UserList + EvalForm + toast)();
}
const UserList = () => "./components/UserList.js";
const EvalForm = () => "./components/EvalForm.js";
const toast = () => "../../../../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    store_user.useUserStore();
    const store = store_index.useIndexStore();
    const ref_toast = common_vendor.ref();
    const formRef = common_vendor.ref(null);
    const searchValue = common_vendor.ref("");
    const list = common_vendor.ref([]);
    const TIdNum = common_vendor.ref(store.submitData.TId);
    const parentId = common_vendor.ref(0);
    const idx = common_vendor.ref(0);
    const oneList = common_vendor.ref([]);
    let oneDepartmentId = "";
    const state = common_vendor.reactive({
      formModel: {
        formData: {
          EvaluatorId: "",
          SubordinateId: "",
          DepartmentId: "",
          Reason: "",
          Lng: "",
          BId: "",
          Evaluator: "",
          Subordinate: ""
        }
      },
      rules: {
        "formData.Evaluator": {
          type: "string",
          required: true,
          message: "请选择评价人",
          trigger: ["blur", "change"]
        },
        "formData.Subordinate": {
          type: "string",
          required: true,
          message: "请选择被评价人",
          trigger: ["blur", "change"]
        },
        "formData.Reason": {
          type: "string",
          required: true,
          message: "请填写理由",
          trigger: ["blur", "change"]
        }
      }
    });
    const previousListData = common_vendor.ref([]);
    const hasListToMerge = common_vendor.ref(false);
    const historyStack = common_vendor.ref([{ id: parentId.value }]);
    const handleSelect = common_vendor.debounce(async (item, index) => {
      var _a;
      idx.value = index;
      const SubordinateVal = state.formModel.formData.Subordinate;
      if (!SubordinateVal && TIdNum.value > 4) {
        historyStack.value.push({
          id: parentId.value,
          listData: list.value
        });
        list.value = (item == null ? void 0 : item.Users) || [];
        return;
      }
      if (TIdNum.value > 3 && item.Id) {
        const currentListData = ((_a = list.value[index]) == null ? void 0 : _a.List) || [];
        parentId.value = item.Id;
        historyStack.value.push({
          id: item.Id,
          listData: currentListData
        });
        if (currentListData.length > 0) {
          previousListData.value = [...currentListData];
          hasListToMerge.value = true;
        } else {
          previousListData.value = [];
          hasListToMerge.value = false;
        }
        await getDepartmentInfoAPI(item.Id);
        return;
      }
      if (item.DepartmentId) {
        parentId.value = item.DepartmentId;
        historyStack.value.push({ id: parentId.value });
        getDepartmentInfoAPI(parentId.value);
      }
    }, 300);
    const getDepartmentInfoAPI = async (id) => {
      var _a;
      const SubordinateVal = state.formModel.formData.Subordinate;
      let myTid = 0;
      if (!SubordinateVal && TIdNum.value === 5) {
        myTid = 2;
      } else {
        myTid = store.submitData.TId;
      }
      const res = await utils_http.getDepartmentInfo({
        ParentId: id,
        TId: myTid
      });
      const data = res.data.Data.listjson;
      common_vendor.index.hideLoading();
      console.log("部门信息 --->", data);
      const sortByManager = (arr) => {
        return arr.sort(
          (a, b) => Number(b.IsManager === true) - Number(a.IsManager === true)
        );
      };
      if (id === 0) {
        oneList.value = sortByManager(data);
      }
      if (!id) {
        list.value = sortByManager(data);
      } else {
        if (TIdNum.value > 3) {
          if (hasListToMerge.value && previousListData.value.length > 0) {
            const mergedData = [...data, ...previousListData.value];
            list.value = sortByManager(mergedData);
          } else {
            list.value = sortByManager(data);
          }
          previousListData.value = [];
          hasListToMerge.value = false;
          return;
        }
        list.value = ((_a = data[idx.value]) == null ? void 0 : _a.Users) || [];
        sortByManager(data[idx.value].Users);
      }
    };
    const onSubordinate = async (e) => {
      var _a;
      if (TIdNum.value === 5) {
        state.formModel.formData.SubordinateId = e.ID;
        state.formModel.formData.Subordinate = e.DisplayName;
        oneDepartmentId = ((_a = oneList.value[idx.value]) == null ? void 0 : _a.DepartmentId) || "";
        historyStack.value.push({
          id: parentId.value,
          listData: list.value,
          type: "subordinate_selected"
        });
        list.value = [];
        getDepartmentInfoAPI(0);
      } else {
        state.formModel.formData.BId = e.ID;
        state.formModel.formData.Subordinate = e.DisplayName;
      }
      if (historyStack.value.length > 1) {
        const prevDepartmentId = historyStack.value[historyStack.value.length - 1].id;
        state.formModel.formData.DepartmentId = TIdNum.value === 5 ? oneDepartmentId : prevDepartmentId;
      }
    };
    const onEvaluator = async (e) => {
      historyStack.value.push({
        id: parentId.value,
        listData: list.value,
        type: "evaluator_selected"
      });
      state.formModel.formData.EvaluatorId = e.ID;
      state.formModel.formData.Evaluator = e.DisplayName;
    };
    const onAssign = async () => {
      var _a, _b, _c, _d;
      try {
        const valid = await ((_a = formRef.value) == null ? void 0 : _a.validate());
        if (valid) {
          let res;
          if (TIdNum.value > 4) {
            res = await utils_http.getAssign(state.formModel.formData);
          } else {
            res = await utils_http.getTransDepartment(state.formModel.formData);
          }
          common_vendor.index.hideLoading();
          console.log("res", res);
          if (res.data.Code != 1) {
            (_b = ref_toast.value) == null ? void 0 : _b.info(res.data.Message);
          } else {
            (_c = ref_toast.value) == null ? void 0 : _c.success(res.data.Message);
            onReset();
          }
        }
      } catch (error) {
        (_d = ref_toast.value) == null ? void 0 : _d.warning("提交失败~");
        console.error("提交失败:", error);
      }
    };
    const onReset = () => {
      if (formRef.value) {
        if (typeof formRef.value.clearValidate === "function") {
          setTimeout(() => {
            formRef.value.clearValidate();
          }, 50);
        }
        if (typeof formRef.value.resetFields === "function") {
          formRef.value.resetFields();
        }
      }
      historyStack.value = [];
      if (TIdNum.value > 3) {
        getDepartmentInfoAPI(0);
      }
    };
    common_vendor.onShow(() => {
      setTimeout(() => {
        getDepartmentInfoAPI(parentId.value);
      }, 300);
    });
    const back = async () => {
      if (historyStack.value.length > 1) {
        const currentLevel = historyStack.value.pop();
        console.log("currentLevel :>> ", currentLevel);
        const prevLevel = historyStack.value[historyStack.value.length - 1];
        parentId.value = prevLevel.id;
        if (prevLevel.listData && prevLevel.listData.length > 0) {
          previousListData.value = [...prevLevel.listData];
          hasListToMerge.value = true;
        } else {
          previousListData.value = [];
          hasListToMerge.value = false;
        }
        if (currentLevel && currentLevel.type === "evaluator_selected") {
          state.formModel.formData.EvaluatorId = "";
          state.formModel.formData.Evaluator = "";
        }
        if (currentLevel && currentLevel.type === "subordinate_selected") {
          state.formModel.formData.SubordinateId = "";
          state.formModel.formData.Subordinate = "";
        }
        await getDepartmentInfoAPI(parentId.value);
      } else {
        console.log("已经在最顶层");
      }
    };
    common_vendor.watch(list, (newValue, oldValue) => {
      if (oldValue && oldValue[idx.value] && oldValue[idx.value].List && oldValue[idx.value].List.length > 0) {
        console.log("list :>> ", newValue);
        const listData = oldValue[idx.value].List;
        previousListData.value = [...listData];
        hasListToMerge.value = true;
      } else {
        previousListData.value = [];
        hasListToMerge.value = false;
      }
    });
    common_vendor.watch(
      historyStack.value,
      (newValue, oldValue) => {
      },
      {
        immediate: true
      }
    );
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: historyStack.value.length > 1 || TIdNum.value === 5 && state.formModel.formData.Subordinate
      }, historyStack.value.length > 1 || TIdNum.value === 5 && state.formModel.formData.Subordinate ? {
        b: common_vendor.o(($event) => back())
      } : {}, {
        c: common_vendor.o(common_vendor.unref(handleSelect)),
        d: common_vendor.o(onSubordinate),
        e: common_vendor.o(onEvaluator),
        f: common_vendor.p({
          list: list.value,
          TIdNum: TIdNum.value,
          selectedSubordinate: state.formModel.formData.Subordinate,
          searchValue: searchValue.value
        }),
        g: TIdNum.value > 3 ? "40vh" : "100%",
        h: common_vendor.sr(formRef, "87f85a6e-1", {
          "k": "formRef"
        }),
        i: common_vendor.o(onAssign),
        j: common_vendor.o(onReset),
        k: common_vendor.p({
          ["form-model"]: state.formModel,
          rules: state.rules,
          TIdNum: TIdNum.value
        }),
        l: common_vendor.sr(ref_toast, "87f85a6e-2", {
          "k": "ref_toast"
        })
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-87f85a6e"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/index/views/department/CrossEvaluation/index.vue"]]);
wx.createComponent(Component);
