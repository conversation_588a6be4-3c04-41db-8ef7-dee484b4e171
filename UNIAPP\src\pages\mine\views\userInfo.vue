<template>
  <view class="pageBox">
    <view class="head">
      <view class="return" @click="store.backAPage()">
        <image
          class="return-img"
          src="../../../static/icons/fanhui.png"
          mode="aspectFit"></image>
      </view>
      <view class="title">个人信息</view>
    </view>
    <view class="main">
      <view class="content" v-for="(item, index) in list" :key="index">
        <view class="label">
          {{ item.name }}
        </view>
        <view class="value">
          <button
            class="specialBtn"
            v-if="index == 0"
            open-type="chooseAvatar"
            @chooseavatar="onChooseAvatar">
            <image
              class="avatar"
              :src="onSrc(item.value)"
              mode="aspectFit"></image>
          </button>
          <!-- 可编辑邮箱 -->
          <input
            class="input"
            type="text"
            v-if="item.key === 'Mail'"
            v-model="item.value" />
          <!-- 正常文字 -->
          <text
            v-if="!whiteList.includes(item.key)"
            @click="OnClicked(item.key, item.value)">
            {{ userStore.userInfo[item.key] || "暂无" }}
          </text>
          <!-- 修改密码 -->
          <view
            class="flex"
            v-if="item.key === 'Password'"
            style="letter-spacing: 6rpx; place-items: center"
            @click="OnClicked(item.key, item.value)">
            <view class="">******</view>
          </view>
          <view
            class=""
            v-if="item.key === 'Mobile'"
            @click="OnClicked(item.key, item.value)">
            {{ hidePhoneNumber(item.value) }}
          </view>
        </view>
      </view>
    </view>
    <view style="width: 60%; margin: 30rpx auto">
      <up-button type="success" @click="onSubmit()">保存</up-button>
    </view>
    <toast ref="ref_toast"></toast>
  </view>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useIndexStore } from "../../../store/index";
import { useUserStore } from "../../../store/user";
import { onShow } from "@dcloudio/uni-app";
import toast from "@/components/toast.vue";
import type { IToast } from "@/types";
import { getUserInfo, setUserEmail } from "@/utils/http";
import { hidePhoneNumber } from "@/utils";
import { cantBeBad } from "@/utils";
import { getBaseUrl } from "@/utils/request";
const store = useIndexStore();
const userStore = useUserStore();
const ref_toast = ref<IToast>();
const whiteList = ref(["Avatar", "Mail", "Password", "Mobile"]);
const list = ref([
  { name: "头像", value: "", key: "Avatar" },
  { name: "姓名", value: "小明", key: "DisplayName" },
  { name: "邮箱", value: "<EMAIL>", key: "Mail" },
  { name: "手机号", value: "未绑定手机号", key: "Mobile" },
  { name: "修改密码", value: "", key: "Password" },
  { name: "部门", value: "无", key: "DepartmentName" },
  { name: "职位", value: "无", key: "Ex4" },
]);

/** 点击的每一项 */
const OnClicked = (keyName: string, value: any) => {
  switch (keyName) {
    case "Mobile":
      uni.navigateTo({
        url: "editPhone?data=" + value,
      });
      break;
    case "Password":
      uni.navigateTo({
        url:
          "editPassword?data=" +
          list.value.find((item) => {
            if (item.key === "Mobile") {
              return item.value;
            }
          }),
      });
      break;
    default:
      break;
  }
};
const onChooseAvatar = (e) => {
  const url = e.detail.avatarUrl;
  uploadImageAPI(url);
  // console.log("头像信息", e);
};
const onSrc = (value: string) => {
  console.log("value", value);

  const http = getBaseUrl().replace(/\Api\/V1$/, "");
  if (!value) {
    return "../../../static/images/avatar.png";
  } else {
    value = http + value;
    return value;
  }
};
//https://192.168.1.233:9225/Uploads/Avatars/19.jpg
// https://360.hlktech.com/Api/V1
// 上传头像
const uploadImageAPI = (filePath: string) => {
  // console.log("filePath", filePath);

  uni.showLoading({
    title: "上传中..",
  });
  const http = getBaseUrl();
  uni.uploadFile({
    url: http + "/User/UpAvatar",
    name: "file",
    fileType: "image",
    filePath, //要上传的文件资源
    success: (response) => {
      // console.log("上传结果：", response);
      let res = JSON.parse(response.data);
      console.log("上传结果：", res.Data);
      if (res.Code === 2) {
        ref_toast.value?.info(res.Message);
        list.value[0].value = "";
        return;
      }

      const url = res.Data;
      uni.hideLoading();
      ref_toast.value?.success("上传成功");
      console.log("url", url);
      userStore.userInfo.Avatar = url;
      list.value[0].value = url;
    },
    fail(err) {
      console.log("上传失败：", err);
      ref_toast.value?.fail("上传失败！");
    },
  });
};
console.log(userStore.userInfo, store.routerUserinfo);
const onSubmit = async () => {
  const op: any = {};
  list.value.map((item) => {
    {
      op[item.key] = item.value;
    }
  });
  const res = await setUserEmail({ Email: op.Mail });
  const code = res.data.Code;
  if (code != 1) {
    ref_toast.value?.fail(res.data.Message);
  } else {
    ref_toast.value?.success("保存成功");
  }
  // console.log("保存结果 ：", res.data);
};
/** 获取用户信息 */
const getUserInfoAPI = async () => {
  const res = await getUserInfo();
  const data = res.data.Data;
  if (data) {
    userStore.userInfo = data;
    console.log("用户信息", data);
    return;
  } else {
    ref_toast.value?.fail(res.data.Message);
  }
};
onShow(async () => {
  await getUserInfoAPI();
  list.value.map((item) => {
    item.value = userStore.userInfo[item.key] ?? "";
  });
});
</script>

<style scoped>
.main {
  background-color: #f7f7f7;
  padding: 10rpx 0rpx;
}

.content {
  width: 100%;
  padding: 30rpx;
  display: flex;
  background-color: white;
  border-bottom: 1px solid #eef1fe;
}

.content:nth-child(1) {
  padding: 30rpx 20rpx 30rpx 30rpx;
  height: 140rpx;
  display: flex;
  place-items: center;
  margin: 10rpx 0rpx;
}

.value {
  color: #888888;
  margin-left: auto;
  margin-right: 20rpx;
  /* border: 2px solid ; */
}

.content:nth-child(1) > .value {
  margin-right: 0rpx !important;
}

.content:nth-child(3) > .value {
  min-width: 380rpx;
  text-align: right;
}

.specialBtn {
  background-color: white;
  border: none;
  box-shadow: none;
  text-align: right;
  -webkit-appearance: none;
  appearance: none;
  color: #888888 !important;
  padding: 0;
  text-decoration: none;
  height: 100%;
  /* border: 2px solid ; */
  display: flex;
  place-items: center;
  justify-content: right;
  border-radius: 50%;
}

.specialBtn::after {
  border: none;
}

.input {
  /* 	max-width: 200rpx;
		min-width: 300rpx; */
  padding: 0px 0rpx 0px 20rpx;
  text-overflow: ellipsis;
  /* border: 1px solid ; */
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 5px;
  overflow: hidden;
  background-color: #ccc;
  box-shadow: 0 0 4px #8b95ed;
  border: 1px solid skyblue;
}

.avatar > image {
  width: 100%;
  height: 100%;
}
</style>
