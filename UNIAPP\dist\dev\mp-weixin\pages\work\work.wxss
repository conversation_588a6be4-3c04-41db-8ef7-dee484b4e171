
.header.data-v-1297a68e {
  padding-top: 14vh;
  background: #5266e2;
  position: relative;
}
.companyBox.data-v-1297a68e {
  position: absolute;
  top: 50%;
  left: 5%;
  margin: auto;
  width: 90%;
  height: 15vh;
  padding: 20rpx;
  background-color: white;
  display: flex;
  flex-direction: column;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(82, 102, 226, 0.1);
  border-radius: 10rpx;
  display: flex;
  justify-content: center;
  place-items: center;
}
.pic.data-v-1297a68e {
  position: absolute;
  top: 32%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 10rpx;
  width: 27vw;
  border-radius: 50%;
  filter: drop-shadow(0px 0 1px #07b3fc);
  /* border: 4px solid white;
		box-shadow: 0 0 4px white; */
}
.pic2.data-v-1297a68e {
  position: absolute;
  top: 42%;
  left: 75%;
  transform: translate(-50%, -50%);
  width: 120rpx;
  height: 44rpx;
}
.name.data-v-1297a68e {
  margin-top: 55rpx;
  letter-spacing: 1px;
  font-size: 31rpx;
  color: #666666;
  padding: 40rpx;
  text-align: center;
  z-index: 100;
}
.main.data-v-1297a68e {
  margin-top: 10vh;
  width: 90%;
  padding: 10rpx 0rpx;
  border-radius: 10px;
  margin-left: 5%;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(82, 102, 226, 0.1);
}
.main > .item.data-v-1297a68e {
  width: 90%;
  height: 110rpx;
  padding: 10rpx 0rpx;
  /* margin: 25rpx auto; */
  border-bottom: 1px solid #f4f4f4;
  display: flex;
  color: var(--bk-3);
}
.item.data-v-1297a68e:last-child {
  border-bottom: none;
}
.item > view.data-v-1297a68e {
  width: 100%;
  height: 100%;
  line-height: 60rpx;
  font-size: var(--size-4);
  letter-spacing: 2rpx;
  display: flex;
  justify-content: center;
  place-items: center;
  /* border: 1px solid #ccc; */
}
.item > view > image.data-v-1297a68e {
  width: 38rpx;
  height: 74rpx;
  /* border: 1px solid gray; */
}
.icons.data-v-1297a68e {
  width: 15vw !important;
}
