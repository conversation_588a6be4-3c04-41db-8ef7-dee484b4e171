"use strict";
const common_vendor = require("../common/vendor.js");
const useUserStore = common_vendor.defineStore("user", {
  // state是一个函数，返回一个对象
  state: () => {
    return {
      DeveloperList: common_vendor.reactive([""]),
      type: 1,
      IsDepartmentlanager: false,
      userInfo: common_vendor.reactive({
        ID: 1,
        DisplayName: "未知用户",
        Sex: 1,
        Ex4: "销售部",
        Avatar: ""
      }),
      /** 清除当前登录用户信息 */
      resetLoginData: function() {
        this.userInfo = {
          ID: 1,
          DisplayName: "未知用户",
          Sex: 1,
          Ex4: "销售部",
          Avatar: ""
        };
        common_vendor.index.clearStorageSync();
      }
    };
  },
  getters: {
    /** 切换菜单 传入 name */
    // resetLoginData(state: any) {
    //   return (name: string) => {
    //   };
    // },
  },
  actions: {}
});
exports.useUserStore = useUserStore;
