"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const utils_http = require("../../../utils/http.js");
const store_user = require("../../../store/user.js");
const utils_index = require("../../../utils/index.js");
require("../../../utils/request.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
if (!Array) {
  const _easycom_up_input2 = common_vendor.resolveComponent("up-input");
  const _easycom_up_form_item2 = common_vendor.resolveComponent("up-form-item");
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  const _easycom_up_form2 = common_vendor.resolveComponent("up-form");
  (_easycom_up_input2 + _easycom_up_form_item2 + _easycom_up_icon2 + _easycom_up_button2 + _easycom_up_form2)();
}
const _easycom_up_input = () => "../../../node-modules/uview-plus/components/u-input/u-input.js";
const _easycom_up_form_item = () => "../../../node-modules/uview-plus/components/u-form-item/u-form-item.js";
const _easycom_up_icon = () => "../../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_button = () => "../../../node-modules/uview-plus/components/u-button/u-button.js";
const _easycom_up_form = () => "../../../node-modules/uview-plus/components/u-form/u-form.js";
if (!Math) {
  (_easycom_up_input + _easycom_up_form_item + _easycom_up_icon + _easycom_up_button + _easycom_up_form + toast)();
}
const toast = () => "../../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "retrievePassword",
  setup(__props) {
    const style = common_vendor.ref({
      marginLeft: "0px",
      fontSize: "60px"
    });
    const store = store_index.useIndexStore();
    store_user.useUserStore();
    const ref_toast = common_vendor.ref();
    const ref_form = common_vendor.ref(null);
    const codeText = common_vendor.ref("获取验证码");
    const list = common_vendor.ref({
      form: {
        Mobile: "",
        SmsCode: "",
        Password: "",
        confirmPassowrd: ""
      },
      rules: {
        "form.Mobile": {
          type: "string",
          required: true,
          trigger: ["change", "blur"],
          asyncValidator: (rule, value, callback) => {
            if (String(value).length === 0) {
              callback(new Error("请输入手机号"));
              return;
            }
            if ((value == null ? void 0 : value.length) < 11 || (value == null ? void 0 : value.length) > 11) {
              callback(new Error("手机号长度非法"));
              return;
            }
            if (!utils_index.phoneRegex.test(value)) {
              callback(new Error("手机号码格式错误"));
              return;
            }
            callback();
          }
        },
        "form.SmsCode": {
          type: "string",
          required: true,
          trigger: ["change", "blur"],
          asyncValidator: (rule, value, callback) => {
            if (String(value).length === 0) {
              callback(new Error("请输入手机验证码"));
              return;
            }
            if ((value == null ? void 0 : value.length) > 6) {
              callback(new Error("验证码长度错误"));
              return;
            }
            callback();
          }
        },
        "form.Password": {
          type: "string",
          required: true,
          trigger: ["change", "blur"],
          asyncValidator: (rule, value, callback) => {
            if (String(value).length === 0) {
              callback(new Error("请输入密码"));
              return;
            }
            if ((value == null ? void 0 : value.length) < 6) {
              callback(new Error("密码至少为6位"));
              return;
            }
            if ((value == null ? void 0 : value.length) >= 28) {
              callback(new Error("密码输入过长"));
              return;
            }
            callback();
          }
        },
        "form.confirmPassowrd": {
          type: "string",
          required: true,
          trigger: ["change", "blur"],
          asyncValidator: (rule, value, callback) => {
            if (String(value).length === 0) {
              callback(new Error("请输入密码"));
              return;
            }
            if (value != list.value.form.Password) {
              callback(new Error("您两次输入的密码不相同"));
              return;
            }
            callback();
          }
        }
      }
    });
    const getCode = async () => {
      var _a, _b;
      if (!utils_index.phoneRegex.test(list.value.form.Mobile)) {
        (_a = ref_toast.value) == null ? void 0 : _a.fail("请输入正确的手机号");
        return;
      }
      if (codeText.value == "获取验证码" || codeText.value == "重新发送") {
        await utils_http.sendSms({ Mobile: list.value.form.Mobile, SmsType: "findpwd" }).then(
          (res) => {
            var _a2, _b2;
            if (res.data.Code != 1) {
              (_a2 = ref_toast.value) == null ? void 0 : _a2.info(res.data.Message);
              return;
            }
            (_b2 = ref_toast.value) == null ? void 0 : _b2.success("发送成功");
            codeText.value = "已发送";
            timerFN(60);
          }
        );
      } else {
        (_b = ref_toast.value) == null ? void 0 : _b.warning("请勿重复获取");
      }
    };
    const timerFN = (time = 60) => {
      let timer = setInterval(() => {
        time -= 1;
        codeText.value = "已发送：" + time;
        if (time <= 0) {
          clearInterval(timer);
          codeText.value = "重新发送";
        }
      }, 1e3);
    };
    const Submit = () => {
      var _a;
      (_a = ref_form.value) == null ? void 0 : _a.validate().then(async (valid) => {
        if (!valid)
          return;
        await updatePasswordAPI();
      }).catch((err) => {
        var _a2;
        console.log("校验失败", err);
        (_a2 = ref_toast.value) == null ? void 0 : _a2.warning("表单验证失败！");
      });
    };
    const updatePasswordAPI = async () => {
      var _a;
      const form = list.value.form;
      const res = await utils_http.findPassword({
        Mobile: form.Mobile,
        Password: form.Password,
        SmsCode: Number(form.SmsCode),
        ComfimPassword: form.confirmPassowrd
      });
      const data = res.data;
      if (data && (data == null ? void 0 : data.Code) != 1) {
        (_a = ref_toast.value) == null ? void 0 : _a.fail(data.Message);
        return;
      }
      common_vendor.index.showModal({
        title: "修改结果🦉",
        content: "密码已修改，点击下方去登录吧",
        showCancel: false,
        confirmText: "好的👌",
        success: () => {
          common_vendor.index.redirectTo({
            url: "../login"
          });
        }
      });
      console.log("修改密码结果-->", data);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(($event) => common_vendor.unref(store).backAPage()),
        b: common_vendor.o(($event) => list.value.form.Mobile = $event),
        c: common_vendor.p({
          type: "text",
          border: "none",
          placeholder: "请输入账号绑定的手机号",
          modelValue: list.value.form.Mobile
        }),
        d: common_vendor.p({
          prop: `form.Mobile`
        }),
        e: common_vendor.t(codeText.value),
        f: codeText.value === "获取验证码" || codeText.value === "重新发送"
      }, codeText.value === "获取验证码" || codeText.value === "重新发送" ? {
        g: common_vendor.p({
          name: "email",
          size: "32",
          color: "#2979ff"
        })
      } : {}, {
        h: common_vendor.o(($event) => getCode()),
        i: common_vendor.p({
          type: "primary",
          plain: true,
          size: "small"
        }),
        j: common_vendor.o(($event) => list.value.form.SmsCode = $event),
        k: common_vendor.p({
          type: "text",
          border: "none",
          placeholder: "请输入手机验证码",
          modelValue: list.value.form.SmsCode
        }),
        l: common_vendor.p({
          prop: `form.SmsCode`
        }),
        m: common_vendor.o(($event) => list.value.form.Password = $event),
        n: common_vendor.p({
          type: "password",
          border: "none",
          placeholder: "请输入新密码",
          modelValue: list.value.form.Password
        }),
        o: common_vendor.p({
          prop: `form.Password`
        }),
        p: common_vendor.o(($event) => list.value.form.confirmPassowrd = $event),
        q: common_vendor.p({
          type: "password",
          border: "none",
          placeholder: "请再次确认密码",
          modelValue: list.value.form.confirmPassowrd
        }),
        r: common_vendor.p({
          prop: `form.confirmPassowrd`
        }),
        s: common_vendor.sr(ref_form, "eef2102f-0", {
          "k": "ref_form"
        }),
        t: common_vendor.p({
          labelPosition: "left",
          model: list.value,
          rules: list.value.rules,
          labelStyle: style.value
        }),
        v: common_vendor.o(($event) => Submit()),
        w: common_vendor.sr(ref_toast, "eef2102f-11", {
          "k": "ref_toast"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-eef2102f"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/login/views/retrievePassword.vue"]]);
wx.createPage(MiniProgramPage);
