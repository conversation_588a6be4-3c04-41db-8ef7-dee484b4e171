<template>
  <view class="cross-evaluation">
    <!-- <view class="inp" v-if="0">
      <up-input class="search" placeholder="请输入成员姓名" prefixIcon="search" @change="oninput"
        prefixIconStyle="font-size: 22px;color: #909399" :autoBlur="true" :focus="true" clearable
        v-model="searchValue"></up-input>
    </view> -->

    <view
      class="flex"
      v-if="
        historyStack.length > 1 ||
        (TIdNum === 5 && state.formModel.formData.Subordinate)
      "
      @click="back()">
      <view class="shang">返回上一级</view>
      <view style="margin-right: 5%; max-height: 40rpx; padding: 10rpx 0px">
        <image
          class="shangImg"
          src="../../../static/index/back.png"
          mode="aspectFit"></image>
      </view>
    </view>
    <scroll-view scroll-y :style="{ height: TIdNum > 3 ? '40vh' : '100%' }">
      <user-list
        :list="list"
        :TIdNum="TIdNum"
        :selectedSubordinate="state.formModel.formData.Subordinate"
        :searchValue="searchValue"
        @select="handleSelect"
        @selectSubordinate="onSubordinate"
        @selectEvaluator="onEvaluator" />
    </scroll-view>

    <!-- 评测表单 -->
    <eval-form
      :form-model="state.formModel"
      :rules="state.rules"
      :TIdNum="TIdNum"
      ref="formRef"
      @submit="onAssign"
      @reset="onReset" />

    <toast ref="ref_toast"></toast>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from "vue";
import { useUserStore } from "@/store/user";
import { useIndexStore } from "@/store";
import UserList from "./components/UserList.vue";
import EvalForm from "./components/EvalForm.vue";
import toast from "@/components/toast.vue";
import {
  getDepartmentInfo,
  searchUserList,
  getAssign,
  getTransDepartment,
} from "@/utils/http";
import { onShow } from "@dcloudio/uni-app";
import debounce from "lodash/debounce";
const userStore = useUserStore();
const store = useIndexStore();
interface FormInstance {
  validate: () => Promise<boolean>;
  resetFields?: () => void;
  clearValidate?: () => void;
}

const ref_toast = ref();
const formRef = ref<FormInstance | null>(null);
const searchValue = ref("");
const list = ref<any>([]);
const TIdNum = ref<number>(store.submitData.TId);
const parentId = ref<string | number>(0);
const idx = ref(0);
const oneList = ref<any[]>([]);
let oneDepartmentId = "";

const state = reactive({
  formModel: {
    formData: {
      EvaluatorId: "",
      SubordinateId: "",
      DepartmentId: "",
      Reason: "",
      Lng: "",
      BId: "",
      Evaluator: "",
      Subordinate: "",
    },
  },
  rules: {
    "formData.Evaluator": {
      type: "string",
      required: true,
      message: "请选择评价人",
      trigger: ["blur", "change"],
    },
    "formData.Subordinate": {
      type: "string",
      required: true,
      message: "请选择被评价人",
      trigger: ["blur", "change"],
    },
    "formData.Reason": {
      type: "string",
      required: true,
      message: "请填写理由",
      trigger: ["blur", "change"],
    },
  },
});

// 保存上一次请求中的List数据
const previousListData = ref<any[]>([]);
// 标记是否有List数据需要合并
const hasListToMerge = ref(false);
// 历史记录栈
const historyStack = ref<
  Array<{ id: string | number; listData?: any[]; type?: string }>
>([{ id: parentId.value }]);
const handleSelect = debounce(async (item: any, index: number) => {
  // console.log(111, item, index);
  idx.value = index;
  const SubordinateVal = state.formModel.formData.Subordinate;
  if (!SubordinateVal && TIdNum.value > 4) {
    historyStack.value.push({
      id: parentId.value,
      listData: list.value,
    });
    list.value = item?.Users || [];
    return;
  }

  //互评申请 | 指定互评
  if (TIdNum.value > 3 && item.Id) {
    const currentListData = list.value[index]?.List || [];
    parentId.value = item.Id;
    historyStack.value.push({
      id: item.Id,
      listData: currentListData,
    });

    // 当点击行的上一层List为空时，不带到下一层
    if (currentListData.length > 0) {
      previousListData.value = [...currentListData];
      hasListToMerge.value = true;
    } else {
      // 如果上一层List为空，则不带到下一层
      previousListData.value = [];
      hasListToMerge.value = false;
    }

    await getDepartmentInfoAPI(item.Id);
    return;
  }

  if (item.DepartmentId) {
    parentId.value = item.DepartmentId;
    // 将新选择的部门ID加入历史栈
    historyStack.value.push({ id: parentId.value });
    getDepartmentInfoAPI(parentId.value);
  } else {
    // console.log("没有");
  }
}, 300);

// 获取部门信息
const getDepartmentInfoAPI = async (id: string | number) => {
  const SubordinateVal = state.formModel.formData.Subordinate;
  let myTid = 0;
  if (!SubordinateVal && TIdNum.value === 5) {
    myTid = 2;
  } else {
    myTid = store.submitData.TId;
  }

  const res = await getDepartmentInfo({
    ParentId: id,
    TId: myTid,
  });
  const data = res.data.Data.listjson;
  uni.hideLoading();
  console.log("部门信息 --->", data);
  // 对数据进行排序处理
  const sortByManager = (arr: any[]) => {
    return arr.sort(
      (a, b) => Number(b.IsManager === true) - Number(a.IsManager === true)
    );
  };

  if (id === 0) {
    oneList.value = sortByManager(data);
  }
  if (!id) {
    list.value = sortByManager(data);
  } else {
    if (TIdNum.value > 3) {
      // 只有当上一层的List不为空时，才将其添加到list.value中
      if (hasListToMerge.value && previousListData.value.length > 0) {
        // 将之前的List数据与新的listjson数据合并
        const mergedData = [...data, ...previousListData.value];
        list.value = sortByManager(mergedData);
      } else {
        list.value = sortByManager(data);
      }
      // 重置状态
      previousListData.value = [];
      hasListToMerge.value = false;
      return;
    }
    list.value = data[idx.value]?.Users || [];
    sortByManager(data[idx.value].Users);
  }
};

// 选择被评人
const onSubordinate = async (e: any) => {
  if (TIdNum.value === 5) {
    state.formModel.formData.SubordinateId = e.ID;
    state.formModel.formData.Subordinate = e.DisplayName;
    oneDepartmentId = oneList.value[idx.value]?.DepartmentId || "";

    // 保存当前状态到historyStack，用于返回时恢复
    // 使用特殊标记 'subordinate_selected' 表示这是选择被评人后的状态
    historyStack.value.push({
      id: parentId.value,
      listData: list.value,
      type: "subordinate_selected",
    });

    list.value = [];
    getDepartmentInfoAPI(0);
  } else {
    state.formModel.formData.BId = e.ID;
    state.formModel.formData.Subordinate = e.DisplayName;
  }

  if (historyStack.value.length > 1) {
    const prevDepartmentId =
      historyStack.value[historyStack.value.length - 1].id;
    state.formModel.formData.DepartmentId =
      TIdNum.value === 5 ? oneDepartmentId : prevDepartmentId;
  }
};

// 选择评测人
const onEvaluator = async (e: any) => {
  // 保存当前状态到historyStack，用于返回时恢复
  // 使用特殊标记 'evaluator_selected' 表示这是选择评测人后的状态
  historyStack.value.push({
    id: parentId.value,
    listData: list.value,
    type: "evaluator_selected",
  });

  state.formModel.formData.EvaluatorId = e.ID;
  state.formModel.formData.Evaluator = e.DisplayName;
};

// 提交表单
const onAssign = async () => {
  try {
    const valid = await formRef.value?.validate();
    if (valid) {
      // 表单验证通过，执行提交逻辑
      let res;
      if (TIdNum.value > 4) {
        res = await getAssign(state.formModel.formData);
      } else {
        res = await getTransDepartment(state.formModel.formData);
      }
      uni.hideLoading();
      console.log("res", res);

      if (res.data.Code != 1) {
        ref_toast.value?.info(res.data.Message);
      } else {
        ref_toast.value?.success(res.data.Message);
        onReset();
      }
    }
  } catch (error) {
    ref_toast.value?.warning("提交失败~");
    console.error("提交失败:", error);
  }
};

// 重置表单
const onReset = () => {
  // 然后清除表单校验提示
  if (formRef.value) {
    // 先调用clearValidate清除校验状态
    if (typeof formRef.value.clearValidate === "function") {
      setTimeout(() => {
        formRef.value.clearValidate();
      }, 50);
    }
    // 再调用resetFields重置表单
    if (typeof formRef.value.resetFields === "function") {
      formRef.value.resetFields();
    }
  }

  historyStack.value = [];
  if (TIdNum.value > 3) {
    getDepartmentInfoAPI(0);
  }
};

onShow(() => {
  setTimeout(() => {
    getDepartmentInfoAPI(parentId.value);
  }, 300);
});
const back = async () => {
  // 如果历史记录中有记录，则只返回上一级，不清空已选择的值
  if (historyStack.value.length > 1) {
    // 移除当前层级
    const currentLevel = historyStack.value.pop();
    console.log("currentLevel :>> ", currentLevel);
    // 获取上一级的信息
    const prevLevel = historyStack.value[historyStack.value.length - 1];
    parentId.value = prevLevel.id;

    // 如果上一级有保存的List数据，设置合并标记
    if (prevLevel.listData && prevLevel.listData.length > 0) {
      previousListData.value = [...prevLevel.listData];
      hasListToMerge.value = true;
    } else {
      previousListData.value = [];
      hasListToMerge.value = false;
    }

    // 如果当前层级是评测人选择层级，则清空评测人信息，保留被评人信息
    if (currentLevel && currentLevel.type === "evaluator_selected") {
      state.formModel.formData.EvaluatorId = "";
      state.formModel.formData.Evaluator = "";
    }

    // 如果当前层级是被评人选择层级，则清空被评人信息
    if (currentLevel && currentLevel.type === "subordinate_selected") {
      state.formModel.formData.SubordinateId = "";
      state.formModel.formData.Subordinate = "";
    }
    await getDepartmentInfoAPI(parentId.value);
  } else {
    console.log("已经在最顶层");
  }
};
watch(list, (newValue, oldValue) => {
  if (
    oldValue &&
    oldValue[idx.value] &&
    oldValue[idx.value].List &&
    oldValue[idx.value].List.length > 0
  ) {
    console.log("list :>> ", newValue);
    // 如果上一次数据中有List子数组且不为空，将其保存下来
    const listData = oldValue[idx.value].List;
    previousListData.value = [...listData];

    hasListToMerge.value = true;
  } else {
    // 如果上一层没有List子数组或为空，不带到下一层
    previousListData.value = [];
    hasListToMerge.value = false;
  }
});
watch(
  historyStack.value,
  (newValue, oldValue) => {
    // console.log("historyStack", newValue);
  },
  {
    immediate: true,
  }
);
// 搜索功能
// const oninput = async (UserName: string) => {
//   list.value = [];
//   if (UserName) {
//     const res = await searchUserList({ UserName });
//     list.value = res.data.Data.listjson;
//   } else {
//     getDepartmentInfoAPI(parentId.value);
//   }
// };
</script>

<style lang="scss" scoped>
.cross-evaluation {
  height: calc(100vh - 13vh);
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  margin-top: 13vh;

  .shang {
    margin: 20rpx 0 20rpx auto;
    color: #666666;
  }

  .shangImg {
    max-height: 50rpx;
    max-width: 40rpx;
    margin-right: 5rpx;
    top: -4px;
  }
}
</style>
