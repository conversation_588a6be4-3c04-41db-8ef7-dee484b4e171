/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-1c933a9a,
.u-empty__wrap.data-v-1c933a9a,
.u-tabs.data-v-1c933a9a,
.u-tabs__wrapper.data-v-1c933a9a,
.u-tabs__wrapper__scroll-view-wrapper.data-v-1c933a9a,
.u-tabs__wrapper__scroll-view.data-v-1c933a9a,
.u-tabs__wrapper__nav.data-v-1c933a9a,
.u-tabs__wrapper__nav__line.data-v-1c933a9a {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
@font-face {
  font-family: "uicon-iconfont";
  src: url("https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf") format("truetype");
}
.u-icon.data-v-1c933a9a {
  display: flex;
  align-items: center;
}
.u-icon--left.data-v-1c933a9a {
  flex-direction: row-reverse;
  align-items: center;
}
.u-icon--right.data-v-1c933a9a {
  flex-direction: row;
  align-items: center;
}
.u-icon--top.data-v-1c933a9a {
  flex-direction: column-reverse;
  justify-content: center;
}
.u-icon--bottom.data-v-1c933a9a {
  flex-direction: column;
  justify-content: center;
}
.u-icon__icon.data-v-1c933a9a {
  font-family: uicon-iconfont;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-icon__icon--primary.data-v-1c933a9a {
  color: #3c9cff;
}
.u-icon__icon--success.data-v-1c933a9a {
  color: #5ac725;
}
.u-icon__icon--error.data-v-1c933a9a {
  color: #f56c6c;
}
.u-icon__icon--warning.data-v-1c933a9a {
  color: #f9ae3d;
}
.u-icon__icon--info.data-v-1c933a9a {
  color: #909399;
}
.u-icon__img.data-v-1c933a9a {
  height: auto;
  will-change: transform;
}
.u-icon__label.data-v-1c933a9a {
  line-height: 1;
}