
.pageBox.data-v-448cd879 {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}
.content.data-v-448cd879 {
  /* position: relative; */
  margin: 5rpx auto 0 auto;
  width: 92%;
  height: 75%;
  /* padding: 0rpx 0px; */
  border-radius: 25rpx;
  z-index: 3;
  /* overflow: hidden; */
  background-color: white;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(4, 0, 0, 0.12);
  /* border: 2px solid green; */
  /* border: 2px solid ; */
}
.content1.data-v-448cd879 {
  position: absolute;
  top: 12.3%;
  left: 5%;
  width: 90%;
  height: 75%;
  /* background: red; */
  background-color: white;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(4, 0, 0, 0.1);
  border-radius: 25rpx;
  z-index: 2;
}
.content2.data-v-448cd879 {
  position: absolute;
  top: 11.7%;
  left: 6%;
  width: 88%;
  height: 75%;
  /* background: green; */
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(4, 0, 0, 0.08);
  border-radius: 25rpx;
  background-color: white;
  z-index: 1;
}
.contentHead.data-v-448cd879 {
  display: flex;
  /* justify-content: space-around; */
  flex-direction: column;
  justify-content: right;
  font-size: 30rpx;
  padding: 20rpx 10rpx 0rpx 10rpx;
  /* border: 2px solid ; */
  text-indent: 10px;
}
.contentHead > view.data-v-448cd879:nth-child(n + 2) {
  padding: 5rpx 0rpx;
}
.contentTitle.data-v-448cd879 {
  width: 100%;
  text-indent: 30rpx;
  font-size: 34rpx;
  line-height: 70rpx;
  height: 80rpx;
  letter-spacing: 1rpx;
  position: relative;
  /* border: 2px solid ; */
}
.contentTitle > image.data-v-448cd879 {
  margin-left: auto;
  margin-right: 40rpx;
  height: 120rpx;
  width: 120rpx;
  border-radius: 10px;
  /* border: 2px solid ; */
}
.contentTitle.data-v-448cd879::before {
  position: absolute;
  content: "";
  left: 5%;
  bottom: 5%;
  width: 60rpx;
  height: 10rpx;
  background: linear-gradient(to right, dodgerblue 5%, blue);
  /* color: dodgerblue; */
  box-shadow: 0 0 2px #ccc;
  border-radius: 10px;
}
.questionContainer.data-v-448cd879 {
  margin-top: 30rpx;
  width: 92%;
  margin-left: 4%;
  height: 66%;
  padding: 10rpx 15rpx;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 3;
  /* border: 2px solid red; */
}
.questionBox.data-v-448cd879 {
  padding: 20rpx;
  /* border: 2px solid red; */
  font-size: 26rpx;
}
.questionContent.data-v-448cd879 {
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
}
.questionTitle.data-v-448cd879 {
  padding: 20rpx 0px;
  letter-spacing: 2rpx;
  font-size: 34rpx;
}
.questionText.data-v-448cd879 {
  color: #393939;
  width: 100%;
  font-size: 28rpx;
  letter-spacing: 1rpx;
  /* border: 2px solid red; */
}
.questionStar.data-v-448cd879 {
  margin-top: 10rpx;
  /* border: 2px solid red; */
  display: flex;
  place-items: center;
}
.tips.data-v-448cd879 {
  margin-top: 30rpx;
  width: 100%;
  /* margin-left: 5%; */
  padding: 10rpx;
  border-radius: 10px;
  z-index: 3;
  color: #666666;
  white-space: nowrap;
  /* border: 1px solid ; */
}
.tips > view.data-v-448cd879:nth-child(1) {
  font-size: 22rpx;
  text-align: center;
}
.tips > view.data-v-448cd879:nth-child(2) {
  margin-top: 10rpx;
  font-size: 23rpx;
  text-align: center;
}
.xavier.data-v-448cd879 {
  margin-top: auto;
  margin-bottom: 10rpx;
  color: transparent;
}
.xavier2.data-v-448cd879 {
  position: fixed;
  bottom: 9%;
  color: #888888;
}
.bottom.data-v-448cd879 {
  padding: 40rpx;
  display: flex;
  width: 100%;
  border-top: 1px solid rgba(240, 240, 240, 1);
  color: #666666;
}
.tx.data-v-448cd879 {
  position: absolute;
  margin-top: 40rpx;
  right: 75rpx;
  width: 20vw;
  height: 20vw;
  box-shadow: 1px 0 4px rgba(240, 240, 240, 1);
  /* border: 2px solid #ccc; */
}


.uicon-star-fill > span {
  margin-left: 50px;
  width: 100px;
  height: 100px;
  padding: 10px;
  background-color: blue;
}
.u-rate__content__item__icon-wrap,
.u-rate__content__item__icon-wrap--half {
  min-width: 50% !important;
}
