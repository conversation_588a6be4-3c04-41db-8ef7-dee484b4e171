<template>
  <view class="hr delHead">
    <Head :title="'人事专用'" returnType="mine"></Head>
    <view class="tip" v-if="tableData.length">
      <up-icon name="error" color="#ff5722"></up-icon>
      以下部门岗位分类总分不为100
    </view>
    <myTable :columns="columns" :data="tableData"></myTable>
    <toast ref="ref_toast"></toast>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Head from "@/components/head.vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { getCheckRationality } from "@/utils/http";
import toast from "@/components/toast.vue";
import type { IToast } from "@/types";
import myTable from "@/components/myTable.vue";
const ref_toast = ref<IToast>();
const hrList = ref([]);
const columns = ref([
  { title: "部门", prop: "Department", width: 1 },
  { title: "职位", prop: "Position", width: 1 },
  { title: "总分", prop: "TotalWeights", width: 1 },
]);
const tableData = ref([]);
onLoad(() => {});
onShow(() => {
  getCRationality();
});
const getCRationality = async () => {
  const res = await getCheckRationality();
  const data = res.data.Data.listjson;
  const code = res.data.Code;
  uni.hideLoading();
  if (code != 1) {
    ref_toast.value?.success("请求失败~");
  } else {
    if (data.length) {
      hrList.value = data;
      tableData.value = data;
    }
  }
};
</script>

<style scoped lang="scss">
.hr {
  .tip {
    display: flex;
    color: #f36666;
    margin-bottom: 20rpx;
    font-size: 24rpx;
  }
}
</style>
