"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Math) {
  toast();
}
const toast = () => "../../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "options",
  setup(__props) {
    const ref_toast = common_vendor.ref();
    const returnSetp = () => {
      common_vendor.index.navigateBack({
        delta: 1
      });
    };
    const onSelect = (val) => {
      var _a;
      if (val === 1) {
        common_vendor.index.redirectTo({
          url: "./retrievePassword"
        });
        return;
      }
      (_a = ref_toast.value) == null ? void 0 : _a.info("请联系管理员");
    };
    common_vendor.onShow((e) => {
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(returnSetp),
        b: common_vendor.o(($event) => onSelect(0)),
        c: common_vendor.o(($event) => onSelect(1)),
        d: common_vendor.sr(ref_toast, "4e8b9ee0-0", {
          "k": "ref_toast"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4e8b9ee0"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/login/views/options.vue"]]);
wx.createPage(MiniProgramPage);
