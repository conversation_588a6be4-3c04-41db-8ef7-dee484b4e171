"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_index = require("../../utils/index.js");
const store_user = require("../../store/user.js");
const store_index = require("../../store/index.js");
const utils_http = require("../../utils/http.js");
require("../../utils/request.js");
require("../../store/pinia.js");
require("../../utils/sign.js");
require("../../utils/sha1.js");
if (!Array) {
  const _easycom_up_notice_bar2 = common_vendor.resolveComponent("up-notice-bar");
  _easycom_up_notice_bar2();
}
const _easycom_up_notice_bar = () => "../../node-modules/uview-plus/components/u-notice-bar/u-notice-bar.js";
if (!Math) {
  (_easycom_up_notice_bar + toast)();
}
const toast = () => "../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const ref_toast = common_vendor.ref();
    const userStore = store_user.useUserStore();
    const store = store_index.useIndexStore();
    const power = common_vendor.ref({});
    const limit = common_vendor.ref(false);
    const showMask = common_vendor.ref(false);
    const deadLine = common_vendor.ref({});
    const list = common_vendor.ref([
      {
        id: 0,
        title: "自评未完成",
        textList: [],
        originList: [],
        url: "/pageA/pages/undone/undone"
      },
      {
        id: 1,
        title: "互评未完成",
        textList: [],
        originList: [],
        url: "/pageA/pages/undone/undone"
      },
      {
        id: 2,
        title: "主管未完成",
        textList: [],
        originList: [],
        url: "/pageA/pages/undone/undone"
      },
      {
        id: 3,
        title: "员工未完成",
        textList: [],
        originList: [],
        url: "/pageA/pages/undone/undone"
      }
    ]);
    common_vendor.onLoad(async () => {
    });
    const toRouter = async (type) => {
      const canProceed = await getDeadlineAPI(type);
      if (canProceed) {
        store.submitData.TId = type - 1;
        common_vendor.index.navigateTo({
          url: "./views/department/index"
        });
      }
    };
    const cache = common_vendor.ref(0);
    const getClassify = async () => {
      var _a;
      if (cache.value === Number(userStore.userInfo.Mobile) && cache.value !== 0) {
        return;
      }
      let res = await utils_http.getIsDepartmentlanager();
      userStore.IsDepartmentlanager = res.data.Data.IsDepartmentManager;
      const data = res.data.Data;
      common_vendor.index.hideLoading();
      if (data && Object.keys(data).length > 0) {
        power.value = data;
        cache.value = Number(userStore.userInfo.Mobile);
      } else {
        (_a = ref_toast.value) == null ? void 0 : _a.info("权限分配失败");
      }
    };
    const getDeadlineAPI = async (type) => {
      var _a, _b;
      const res = await utils_http.getDeadline();
      deadLine.value = res.data.Data;
      common_vendor.index.hideLoading();
      let currentDate = /* @__PURE__ */ new Date();
      let currentDay = currentDate.getDate();
      if (type > 4) {
        if (currentDay >= deadLine.value.SubmitDay && currentDay <= deadLine.value.AssignDay) {
          return true;
        } else {
          (_a = ref_toast.value) == null ? void 0 : _a.warning(
            `只能在每月${deadLine.value.SubmitDay}-${deadLine.value.AssignDay}号期间进行操作`
          );
          return false;
        }
      }
      if (type < 5 || type === -1) {
        if (currentDay >= deadLine.value.UrgedDay && currentDay <= deadLine.value.CompleteDay) {
          return true;
        } else {
          if (type !== -1) {
            (_b = ref_toast.value) == null ? void 0 : _b.warning(
              `只能在每月${deadLine.value.UrgedDay}-${deadLine.value.CompleteDay}号期间进行操作`
            );
          }
          return false;
        }
      }
    };
    const getNotice = async () => {
      var _a;
      try {
        common_vendor.index.showLoading({ title: "获取通知中..." });
        const apiConfigs = [
          {
            api: utils_http.getSelfNotice,
            index: 0,
            nameField: "DisplayName"
          },
          {
            api: utils_http.getMutualNotice,
            index: 1,
            nameField: "UserName"
          },
          {
            api: utils_http.getToptoBottom,
            index: 2,
            nameField: "SupervisorName"
          },
          {
            api: utils_http.getBottomtoTop,
            index: 3,
            nameField: "SubordinateName"
          }
        ];
        const results = await Promise.all(apiConfigs.map((config) => config.api()));
        results.forEach((res, i) => {
          const config = apiConfigs[i];
          const data = res.data.Data || [];
          list.value[config.index].originList = data;
          if (data.length) {
            list.value[config.index].textList = data.map(
              (item) => item[config.nameField]
            );
          } else {
            list.value[config.index].textList = [];
          }
        });
      } catch (error) {
        console.error("获取通知失败:", error);
        (_a = ref_toast.value) == null ? void 0 : _a.netFail("获取通知失败，请稍后重试");
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    const onNotice = (t) => {
      var _a;
      console.log("t :>> ", t);
      if (!t || !t.textList || t.textList.length === 0) {
        (_a = ref_toast.value) == null ? void 0 : _a.info("没有可显示的通知数据");
        return;
      }
      common_vendor.index.redirectTo({
        url: `/pageA/pages/undone/undone?data=${encodeURIComponent(
          JSON.stringify(t)
        )}`
      });
    };
    const useWX = () => {
      showMask.value = false;
      common_vendor.index.getUserProfile({
        desc: "请先登录",
        provider: "weixin",
        success: function(info) {
          var _a;
          console.log("用户信息为：", info);
          userStore.userInfo.Avatar = info.userInfo.avatarUrl;
          (_a = ref_toast.value) == null ? void 0 : _a.success("授权成功~");
        },
        fail: function(err) {
          userStore.userInfo.Avatar = "/static/images/avatar.png";
          console.log("用户拒绝授权", err);
        }
      });
    };
    const isLoginFn = () => {
      if (!common_vendor.index.getStorageSync("AccessToken") || !common_vendor.index.getStorageSync("openid")) {
        return false;
      }
      if (userStore.userInfo.DisplayName === "未知用户" || !userStore.userInfo.DisplayName) {
        getUserInfoAPI();
        return true;
      }
      return true;
    };
    const refreshTokenAPI = async () => {
      await utils_index.getCurrentEnvironment().then(async (url) => {
        var _a, _b;
        if (url.length >= 5) {
          const res = await utils_http.refreshToken({
            OpenId: common_vendor.index.getStorageSync("openid")
          });
          if (!res.data.Data) {
            (_a = ref_toast.value) == null ? void 0 : _a.info(res.data.Message);
            return;
          }
          if (!res.data.Data.User) {
            common_vendor.index.hideLoading();
            (_b = ref_toast.value) == null ? void 0 : _b.info("用户信息获取失败");
            setTimeout(() => {
              common_vendor.index.redirectTo({
                url: "../login/login"
              });
            }, 1e3);
            return;
          }
          common_vendor.index.setStorageSync("AccessToken", res.data.Data.Token.AccessToken);
          common_vendor.index.setStorageSync("RefreshToken", res.data.Data.Token.RefreshToken);
          userStore.userInfo = res.data.Data.User;
          console.log("刷新token -->", res);
        }
      }).catch((err) => {
      });
    };
    const getOpenid = () => {
      common_vendor.index.login({
        success: (res) => {
          console.log("code 信息 -->", res);
          utils_http.getOpenidAPI({ code: res.code }).then(async (res2) => {
            console.log("index--openid -->", res2);
            if (res2.statusCode === 200 || res2.statusCode === 1) {
              common_vendor.index.setStorageSync("openid", res2.data.Data.openid);
              await refreshTokenAPI();
              return;
            }
            common_vendor.index.redirectTo({
              url: "../login/login"
            });
          }).catch((err) => {
            var _a;
            common_vendor.index.hideLoading();
            (_a = ref_toast.value) == null ? void 0 : _a.netFail("网络请求失败");
            console.log("获取openid失败-->", err);
          });
        },
        fail: (err) => {
          var _a;
          console.log("调用失败，您当前不在微信环境 -->", err);
          (_a = ref_toast.value) == null ? void 0 : _a.netFail("请勿运行于非微信环境！");
        }
      });
    };
    const getUserInfoAPI = async () => {
      var _a;
      const res = await utils_http.getUserInfo();
      const data = res.data.Data;
      if (data) {
        userStore.userInfo = data;
        console.log("用户信息", data);
        return;
      } else {
        (_a = ref_toast.value) == null ? void 0 : _a.fail(res.data.Message);
      }
    };
    common_vendor.onShow(async () => {
      utils_index.getCurrentEnvironment().then(async (url) => {
        if (url.length >= 5) {
          store.isWx = true;
        } else {
          store.isWx = false;
        }
        store.resetData();
      });
      if (!isLoginFn()) {
        getOpenid();
        return;
      }
      const bool = await getDeadlineAPI(-1);
      limit.value = bool || false;
      if (limit.value) {
        getNotice();
      }
      getClassify();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: limit.value
      }, limit.value ? {
        b: common_vendor.f(list.value, (t, i, i0) => {
          return common_vendor.e({
            a: t.textList.length
          }, t.textList.length ? {
            b: common_vendor.t(t.title),
            c: common_vendor.o(($event) => onNotice(t), t.id),
            d: "83a5a03c-0-" + i0,
            e: common_vendor.p({
              text: t.textList,
              direction: "column"
            })
          } : {}, {
            f: t.id
          });
        })
      } : {}, {
        c: common_vendor.o(($event) => toRouter(1)),
        d: !power.value.IsDepartmentManager
      }, !power.value.IsDepartmentManager ? {
        e: common_vendor.o(($event) => toRouter(2))
      } : {}, {
        f: power.value.IsDepartmentManager
      }, power.value.IsDepartmentManager ? {
        g: common_vendor.o(($event) => toRouter(3))
      } : {}, {
        h: common_vendor.o(($event) => toRouter(4)),
        i: common_vendor.o(($event) => toRouter(5)),
        j: power.value.IsDepartmentManager
      }, power.value.IsDepartmentManager ? {
        k: common_vendor.o(($event) => toRouter(6))
      } : {}, {
        l: common_vendor.sr(ref_toast, "83a5a03c-1", {
          "k": "ref_toast"
        }),
        m: showMask.value
      }, showMask.value ? {
        n: common_vendor.o(($event) => useWX())
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-83a5a03c"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/index/index.vue"]]);
wx.createPage(MiniProgramPage);
