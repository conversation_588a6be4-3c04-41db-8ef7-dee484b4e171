"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const utils_http = require("../../../utils/http.js");
require("../../../utils/request.js");
require("../../../utils/index.js");
require("../../../store/user.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "tablePage",
  setup(__props) {
    const store = store_index.useIndexStore();
    const text = common_vendor.ref(``);
    const formData = common_vendor.ref({});
    const onFen = (item) => {
      if (!item || !item.List0 || !Array.isArray(item.List0) || item.List0.length === 0) {
        return "0.00";
      }
      const totalWeight = item.Weights;
      const totalScore = item.List0.reduce((sum, subItem) => {
        const subScore = totalWeight * (subItem.Weight / 100) * (subItem.Fraction / 5);
        if (typeof subItem === "object") {
          subItem.xiaofen = subScore;
        }
        return sum + subScore;
      }, 0);
      return totalScore.toFixed(2);
    };
    const getAHistoryItemAPI = async (TId, TimeDate) => {
      var _a, _b;
      const res = await utils_http.getAHistoryItem({ TId, TimeDate });
      const data = (_b = (_a = res.data) == null ? void 0 : _a.Data) == null ? void 0 : _b.listjson[0];
      if (!data) {
        common_vendor.index.showToast({
          icon: "none",
          title: "无数据",
          duration: 3e3
        });
        setTimeout(() => {
          common_vendor.index.navigateBack({ delta: 1 });
        }, 2e3);
        return;
      }
      formData.value = data;
      formData.value.Content = formData.value.Content;
      text.value = (data == null ? void 0 : data.Reamrk) || "";
      console.log(data);
    };
    const getAHistoryItemAPI2 = async (index, TId, TimeDate) => {
      var _a, _b;
      const res = await utils_http.getAHistoryItem({ TId, TimeDate });
      const data = (_b = (_a = res.data) == null ? void 0 : _a.Data) == null ? void 0 : _b.listjson[index];
      if (!data)
        return;
      formData.value = data;
      formData.value.UserName = data == null ? void 0 : data.BserName;
      formData.value.DepartmentName = data == null ? void 0 : data.BepartmentName;
      formData.value.Content = formData.value.Content;
      text.value = text.value = (data == null ? void 0 : data.Reamrk) || "";
    };
    common_vendor.onLoad((e) => {
      if (e.type && e.date) {
        getAHistoryItemAPI(e.type, e.date);
        console.log("自评", e);
      }
      if (e.data) {
        const data = JSON.parse(e.data);
        console.log("除自评以外的", data);
        getAHistoryItemAPI2(data.index, data.type, data.date);
      }
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e;
      return {
        a: common_vendor.o(($event) => common_vendor.unref(store).backAPage()),
        b: common_vendor.t((_a = formData.value) == null ? void 0 : _a.UserName),
        c: common_vendor.t(((_b = formData.value) == null ? void 0 : _b.DepartmentName) || "无"),
        d: common_vendor.t(((_d = (_c = formData.value) == null ? void 0 : _c.CreateTime) == null ? void 0 : _d.split(" ")[0]) || "未知日期"),
        e: common_vendor.f((_e = formData.value) == null ? void 0 : _e.Content, (item1, k0, i0) => {
          return {
            a: common_vendor.t(item1.Name),
            b: common_vendor.t(item1.Weights),
            c: common_vendor.f(item1 == null ? void 0 : item1.List0, (item2, index2, i1) => {
              return {
                a: common_vendor.t(index2 + 1),
                b: common_vendor.t(item2.Content),
                c: common_vendor.t(item2.Weight + "%"),
                d: common_vendor.t(item2.Fraction),
                e: item2
              };
            }),
            d: common_vendor.t(onFen(item1)),
            e: item1
          };
        }),
        f: common_vendor.t(formData.value.Score),
        g: text.value.length,
        h: "/1000",
        i: text.value,
        j: common_vendor.o(($event) => text.value = $event.detail.value)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-df564ecf"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/work/views/tablePage.vue"]]);
wx.createPage(MiniProgramPage);
