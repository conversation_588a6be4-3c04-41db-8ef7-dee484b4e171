<template>
  <view class="pageBox">
    <view class="head"></view>
    <!-- 通知 -->
    <view class="notice" v-if="limit">
      <view class="noticeBox" v-for="(t, i) in list" :key="t.id">
        <template v-if="t.textList.length">
          <view class="yellow">{{ t.title }}</view>
          <up-notice-bar
            @click="onNotice(t)"
            :text="t.textList"
            direction="column"></up-notice-bar>
        </template>
      </view>
    </view>
    <view class="titleBox" style="font-size: var(--size-8)">
      360绩效考核
      <view
        class=""
        style="
          font-size: var(--size-2);
          color: var(--info);
          padding-top: 20rpx;
        ">
        请选择您要评测的类型
      </view>
    </view>
    <view class="functionBox">
      <view class="card" @click="toRouter(1)">
        自评
        <image
          class="cardImage"
          src="../../static/index/icon1.png"
          mode="aspectFit"></image>
      </view>
      <view v-if="!power.IsDepartmentManager" class="card" @click="toRouter(2)">
        互评
        <image
          class="cardImage"
          src="../../static/index/icon2.png"
          mode="aspectFit"></image>
      </view>
      <view v-if="power.IsDepartmentManager" class="card" @click="toRouter(3)">
        上级对下级
        <image
          class="cardImage"
          src="../../static/index/icon3.png"
          mode="aspectFit"></image>
      </view>
      <view class="card" @click="toRouter(4)">
        下级对上级
        <image
          class="cardImage"
          src="../../static/index/icon4.png"
          mode="aspectFit"></image>
      </view>
      <view class="card" @click="toRouter(5)">
        <view>
          申请跨部门
          <br />
          互评
        </view>
        <image
          class="cardImage"
          src="../../static/index/kuabumen.png"
          mode="aspectFit"></image>
      </view>
      <view v-if="power.IsDepartmentManager" class="card" @click="toRouter(6)">
        指定互评
        <image
          class="cardImage"
          src="../../static/index/zhidinghuping.png"
          mode="aspectFit"></image>
      </view>
    </view>
    <toast ref="ref_toast"></toast>
    <view class="mask" v-if="showMask" @click="useWX()"></view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import toast from "../../components/toast.vue";
import { generateHash, getCurrentEnvironment } from "@/utils";
import { useUserStore } from "@/store/user";
import { useIndexStore } from "@/store";
import {
  getOpenidAPI,
  getUserInfo,
  refreshToken,
  getIsDepartmentlanager,
  getDeadline,
  getSelfNotice,
  getMutualNotice,
  getToptoBottom,
  getBottomtoTop,
} from "@/utils/http";
import { checkLogin } from "@/utils/request";
import type { IToast, Deadline } from "@/types/index.ts";
const ref_toast = ref<IToast>();
const userStore = useUserStore();
const store = useIndexStore();
const power = ref({});
const limit = ref(false);
const showMask = ref(false);
const deadLine = ref<Deadline>({} as Deadline);
const list = ref([
  {
    id: 0,
    title: "自评未完成",
    textList: [],
    originList: [],
    url: "/pageA/pages/undone/undone",
  },
  {
    id: 1,
    title: "互评未完成",
    textList: [],
    originList: [],
    url: "/pageA/pages/undone/undone",
  },
  {
    id: 2,
    title: "主管未完成",
    textList: [],
    originList: [],
    url: "/pageA/pages/undone/undone",
  },
  {
    id: 3,
    title: "员工未完成",
    textList: [],
    originList: [],
    url: "/pageA/pages/undone/undone",
  },
]);

onLoad(async () => {});
const toRouter = async (type: number) => {
  const canProceed = await getDeadlineAPI(type);
  // 如果可以继续（日期在允许范围内），则导航到评测页面
  if (canProceed) {
    store.submitData.TId = type - 1;
    uni.navigateTo({
      url: "./views/department/index",
    });
  }
};
// 使用用户ID来跟踪权限信息是否需要刷新
const cache = ref(0);
// 分类
const getClassify = async () => {
  // 如果当前用户ID与上次获取权限时的用户ID相同，则跳过
  if (cache.value === Number(userStore.userInfo.Mobile) && cache.value !== 0) {
    // console.log("使用缓存的权限数据");
    return;
  }
  let res = await getIsDepartmentlanager();
  userStore.IsDepartmentlanager = res.data.Data.IsDepartmentManager;
  const data = res.data.Data;
  uni.hideLoading();
  if (data && Object.keys(data).length > 0) {
    power.value = data;
    // 记录当前用户ID
    cache.value = Number(userStore.userInfo.Mobile);
  } else {
    ref_toast.value?.info("权限分配失败");
  }
};

const getDeadlineAPI = async (type: number) => {
  const res = await getDeadline();
  // console.log("首页截止日期", res);
  deadLine.value = res.data.Data;
  uni.hideLoading();
  // "SubmitDay":   5, 提出跨部门打分申请的开始日期
  // "AssignDay":   8, 主管审核/指定跨部门打分的截止日期
  // "UrgedDay":    10, 人事催促打分截止日期
  // "CompleteDay": 12 停止打分日期

  // 获取当前日期
  let currentDate = new Date();
  let currentDay = currentDate.getDate();
  // console.log("当前日期", currentDay, deadLine.value);
  // 检查当前日期是否在每月5-8号范围内
  // return true;
  if (type > 4) {
    if (
      currentDay >= deadLine.value.SubmitDay &&
      currentDay <= deadLine.value.AssignDay
    ) {
      return true;
    } else {
      ref_toast.value?.warning(
        `只能在每月${deadLine.value.SubmitDay}-${deadLine.value.AssignDay}号期间进行操作`
      );
      return false;
    }
  }

  if (type < 5 || type === -1) {
    if (
      currentDay >= deadLine.value.UrgedDay &&
      currentDay <= deadLine.value.CompleteDay
    ) {
      return true;
    } else {
      if (type !== -1) {
        ref_toast.value?.warning(
          `只能在每月${deadLine.value.UrgedDay}-${deadLine.value.CompleteDay}号期间进行操作`
        );
      }
      return false;
    }
  }
};

const getNotice = async () => {
  try {
    uni.showLoading({ title: "获取通知中..." });

    // 定义API调用和对应的数据处理配置
    const apiConfigs = [
      {
        api: getSelfNotice,
        index: 0,
        nameField: "DisplayName",
      },
      {
        api: getMutualNotice,
        index: 1,
        nameField: "UserName",
      },
      {
        api: getToptoBottom,
        index: 2,
        nameField: "SupervisorName",
      },
      {
        api: getBottomtoTop,
        index: 3,
        nameField: "SubordinateName",
      },
    ];

    // 并行请求所有API
    const results = await Promise.all(apiConfigs.map((config) => config.api()));

    // 处理每个API的结果
    results.forEach((res, i) => {
      const config = apiConfigs[i];
      const data = res.data.Data || [];

      // 更新原始数据
      list.value[config.index].originList = data;

      // 提取显示文本
      if (data.length) {
        list.value[config.index].textList = data.map(
          (item: any) => item[config.nameField]
        );
      } else {
        list.value[config.index].textList = [];
      }
    });
  } catch (error) {
    console.error("获取通知失败:", error);
    ref_toast.value?.netFail("获取通知失败，请稍后重试");
  } finally {
    uni.hideLoading();
  }
};
const onNotice = (t: any) => {
  console.log("t :>> ", t);
  if (!t || !t.textList || t.textList.length === 0) {
    ref_toast.value?.info("没有可显示的通知数据");
    return;
  }
  uni.redirectTo({
    url: `/pageA/pages/undone/undone?data=${encodeURIComponent(
      JSON.stringify(t)
    )}`,
  });
};

const useWX = () => {
  showMask.value = false;
  uni.getUserProfile({
    desc: "请先登录",
    provider: "weixin",
    success: function (info) {
      console.log("用户信息为：", info);
      userStore.userInfo.Avatar = info.userInfo.avatarUrl;
      ref_toast.value?.success("授权成功~");
    },
    fail: function (err) {
      userStore.userInfo.Avatar = "/static/images/avatar.png"; //会变成默认头像
      console.log("用户拒绝授权", err);
    },
  });
};

const isLoginFn = () => {
  if (!uni.getStorageSync("AccessToken") || !uni.getStorageSync("openid")) {
    return false;
  }
  if (
    userStore.userInfo.DisplayName === "未知用户" ||
    !userStore.userInfo.DisplayName
  ) {
    getUserInfoAPI();
    return true;
  }
  return true;
};
/** 是微信环境 就刷新token和用户信息 */
const refreshTokenAPI = async () => {
  // 03.有token 及 openid 验证一下是否有过期
  await getCurrentEnvironment()
    .then(async (url) => {
      if (url.length >= 5) {
        //微信环境才去拿openid-刷新用户信息
        const res = await refreshToken({
          OpenId: uni.getStorageSync("openid"),
        });
        if (!res.data.Data) {
          ref_toast.value?.info(res.data.Message);
          return;
        }
        // 如果 openid 给不出数据来
        if (!res.data.Data.User) {
          uni.hideLoading();
          ref_toast.value?.info("用户信息获取失败");
          setTimeout(() => {
            uni.redirectTo({
              url: "../login/login",
            });
          }, 1000);
          return;
        }
        uni.setStorageSync("AccessToken", res.data.Data.Token.AccessToken);
        uni.setStorageSync("RefreshToken", res.data.Data.Token.RefreshToken);
        // uni.setStorageSync('userInfo', JSON.stringify(res.data.Data.User))
        userStore.userInfo = res.data.Data.User;
        console.log("刷新token -->", res);
      }
    })
    .catch((err) => {
      new Error(err);
    });
};

/** 调用code 获取openid */
const getOpenid = () => {
  // uni.showLoading({title:'数据获取中...'})
  uni.login({
    success: (res) => {
      console.log("code 信息 -->", res);
      getOpenidAPI({ code: res.code })
        .then(async (res) => {
          console.log("index--openid -->", res);
          if (res.statusCode === 200 || res.statusCode === 1) {
            uni.setStorageSync("openid", res.data.Data.openid);
            await refreshTokenAPI();
            return;
          }
          // 报错了，或者说  没注册
          uni.redirectTo({
            url: "../login/login",
          });
        })
        .catch((err) => {
          uni.hideLoading();
          ref_toast.value?.netFail("网络请求失败");
          console.log("获取openid失败-->", err);
        });
    },
    fail: (err) => {
      console.log("调用失败，您当前不在微信环境 -->", err);
      ref_toast.value?.netFail("请勿运行于非微信环境！");
    },
  });
};
/** 获取用户信息 */
const getUserInfoAPI = async () => {
  const res = await getUserInfo();
  const data = res.data.Data;
  if (data) {
    userStore.userInfo = data;
    console.log("用户信息", data);
    return;
  } else {
    ref_toast.value?.fail(res.data.Message);
  }
};
onShow(async () => {
  // console.log("onShow");
  getCurrentEnvironment().then(async (url) => {
    if (url.length >= 5) {
      //微信环境
      store.isWx = true;
    } else {
      store.isWx = false;
    }
    // 重置提交页所需的数据，避免bug
    store.resetData();
  });

  if (!isLoginFn()) {
    getOpenid();
    return;
  }
  // 通知限时显示 10-12号
  const bool = await getDeadlineAPI(-1);
  // console.log("bool", bool);
  limit.value = bool || false;
  if (limit.value) {
    getNotice();
  }
  getClassify();
});
</script>

<style scoped>
.pageBox {
  padding: 15rpx;
}

.titleBox {
  width: 100%;
  padding: 20rpx;
  margin-top: 10px;
}

.functionBox {
  margin-top: 30rpx;
  width: 100%;
  padding: 20rpx;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 40rpx;
}

.icon {
  width: 50rpx;
  height: 50rpx;
}

.card {
  width: 42vw;
  height: 155rpx;
  background: var(--background-login);
  border-radius: 6px;
  font-size: var(--size-3);
  box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0, 0, 0, 0.2);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 26rpx;
}

.cardImage {
  width: 80rpx;
  height: 80rpx;
  padding-left: 0rpx;
}

.cardImage:last-child {
  border-radius: 50%;
}

.mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.noticeBox {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}

:deep(.u-icon__icon) {
  font-size: 14px !important;
}

:deep(.u-notice__swiper__item__text) {
  font-size: 14px !important;
}

.yellow {
  background-color: #fdf6ec;
  height: 34px;
  line-height: 34px;
  padding-left: 20rpx;
  font-weight: bold;
  color: red;
  font-size: 14px;
}
</style>
