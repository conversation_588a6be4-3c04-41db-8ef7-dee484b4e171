<template>
  <view class="pageBox">
    <view class="head">
      <view class="return" @click="store.backAPage()">
        <image
          class="return-img"
          src="../../../static/icons/fanhui.png"
          mode="aspectFit"></image>
      </view>
      <view class="title">找回密码</view>
    </view>
    <view class="main">
      <up-form
        labelPosition="left"
        :model="list"
        :rules="list.rules"
        ref="ref_form"
        :labelStyle="style">
        <view class="item" style="border-bottom: 1px solid #e5e5e5">
          <up-form-item :prop="`form.Mobile`">
            <up-input
              class="input"
              type="text"
              v-model="list.form.Mobile"
              border="none"
              placeholder="请输入账号绑定的手机号">
              <template #prefix>
                <view
                  style="
                    border-right: 1rpx solid #e5e5e5;
                    padding-right: 30rpx;
                    display: flex;
                    place-items: center;
                  ">
                  <view class="">手机号</view>
                </view>
              </template>
            </up-input>
          </up-form-item>
        </view>
        <view class="item" style="border-bottom: 1px solid #e5e5e5">
          <up-form-item :prop="`form.SmsCode`">
            <!-- 	<up-input class="input" type="text" v-model="list.form.SmsCode" border="none" placeholder="请输入旧密码">
							<template #prefix>
								<view
									style="border-right:1rpx solid #E5E5E5;padding-right: 30rpx;display: flex;place-items: center;">
									<view class="">
										验证码
									</view>
								</view>
							</template>
						</up-input> -->
            <up-input
              class="input"
              type="text"
              v-model="list.form.SmsCode"
              border="none"
              placeholder="请输入手机验证码">
              <template #prefix>
                <view
                  style="
                    border-right: 1rpx solid #e5e5e5;
                    padding-right: 30rpx;
                    display: flex;
                    place-items: center;
                  ">
                  <view class="">验证码</view>
                </view>
              </template>
              <template #suffix>
                <view class="">
                  <up-button
                    type="primary"
                    plain
                    size="small"
                    @click="getCode()">
                    <view class="">
                      {{ codeText }}
                    </view>
                    <view
                      class=""
                      v-if="
                        codeText === '获取验证码' || codeText === '重新发送'
                      ">
                      <up-icon name="email" size="32" color="#2979ff"></up-icon>
                    </view>
                  </up-button>
                </view>
              </template>
            </up-input>
          </up-form-item>
        </view>
        <view class="item" style="border-bottom: 1px solid #e5e5e5">
          <up-form-item :prop="`form.Password`">
            <up-input
              class="input"
              type="password"
              v-model="list.form.Password"
              border="none"
              placeholder="请输入新密码">
              <template #prefix>
                <view
                  style="
                    border-right: 1rpx solid #e5e5e5;
                    padding-right: 30rpx;
                    display: flex;
                    place-items: center;
                  ">
                  <view class="">新密码</view>
                </view>
              </template>
            </up-input>
          </up-form-item>
        </view>
        <view class="item">
          <up-form-item :prop="`form.confirmPassowrd`">
            <up-input
              class="input"
              type="password"
              v-model="list.form.confirmPassowrd"
              border="none"
              placeholder="请再次确认密码">
              <template #prefix>
                <view
                  style="
                    border-right: 1rpx solid #e5e5e5;
                    padding-right: 30rpx;
                    display: flex;
                    place-items: center;
                  ">
                  <view class="">确认密码</view>
                </view>
              </template>
            </up-input>
          </up-form-item>
        </view>
      </up-form>
      <view class="" style="margin-top: 50rpx; transition: all 0.5s">
        <button
          style="
            width: 80%;
            background: linear-gradient(80deg, #677bf0, #2330df);
            box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0, 0, 0, 0.2);
            color: white;
            border-radius: 0rpx 41rpx 41rpx;
          "
          @click="Submit()">
          提交
        </button>
      </view>
      <!--  -->
    </view>
    <toast ref="ref_toast"></toast>
  </view>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useIndexStore } from "@/store";
import toast from "@/components/toast.vue";
import type { IToast } from "@/types";
import { findPassword, sendSms } from "@/utils/http";
import { useUserStore } from "@/store/user";
import { phoneRegex } from "../../../utils";
const style = ref({
  marginLeft: "0px",
  fontSize: "60px",
});
const store = useIndexStore();
const userStore = useUserStore();
const ref_toast = ref<IToast>();
const ref_form = ref<any>(null)!;
const codeText = ref("获取验证码");
const list = ref({
  form: {
    Mobile: "",
    SmsCode: "",
    Password: "",
    confirmPassowrd: "",
  },
  rules: {
    "form.Mobile": {
      type: "string",
      required: true,
      trigger: ["change", "blur"],
      asyncValidator: (rule, value: string, callback) => {
        // console.log('验证通过 -->', value);
        if (String(value).length === 0) {
          callback(new Error("请输入手机号"));
          return;
        }
        if (value?.length < 11 || value?.length > 11) {
          callback(new Error("手机号长度非法"));
          return;
        }
        if (!phoneRegex.test(value)) {
          callback(new Error("手机号码格式错误"));
          return;
        }
        callback();
      },
    },
    "form.SmsCode": {
      type: "string",
      required: true,
      trigger: ["change", "blur"],
      asyncValidator: (rule, value: string, callback) => {
        if (String(value).length === 0) {
          callback(new Error("请输入手机验证码"));
          return;
        }
        if (value?.length > 6) {
          callback(new Error("验证码长度错误"));
          return;
        }
        callback();
      },
    },
    "form.Password": {
      type: "string",
      required: true,
      trigger: ["change", "blur"],
      asyncValidator: (rule, value: string, callback) => {
        if (String(value).length === 0) {
          callback(new Error("请输入密码"));
          return;
        }
        if (value?.length < 6) {
          callback(new Error("密码至少为6位"));
          return;
        }
        if (value?.length >= 28) {
          callback(new Error("密码输入过长"));
          return;
        }
        callback();
      },
    },
    "form.confirmPassowrd": {
      type: "string",
      required: true,
      trigger: ["change", "blur"],
      asyncValidator: (rule, value: string, callback) => {
        if (String(value).length === 0) {
          callback(new Error("请输入密码"));
          return;
        }
        if (value != list.value.form.Password) {
          callback(new Error("您两次输入的密码不相同"));
          return;
        }
        callback();
        // console.log('验证通过 -->', value);
      },
    },
  },
});
// 函数 15361580137
const getCode = async () => {
  if (!phoneRegex.test(list.value.form.Mobile)) {
    ref_toast.value?.fail("请输入正确的手机号");
    return;
  }
  if (codeText.value == "获取验证码" || codeText.value == "重新发送") {
    await sendSms({ Mobile: list.value.form.Mobile, SmsType: "findpwd" }).then(
      (res) => {
        // console.log('短信',res);
        if (res.data.Code != 1) {
          ref_toast.value?.info(res.data.Message);
          return;
        }
        ref_toast.value?.success("发送成功");
        codeText.value = "已发送";
        timerFN(60);
      }
    );
  } else {
    ref_toast.value?.warning("请勿重复获取");
  }
};
const timerFN = (time = 60) => {
  let timer = setInterval(() => {
    time -= 1;
    codeText.value = "已发送：" + time;
    if (time <= 0) {
      clearInterval(timer);
      codeText.value = "重新发送";
    }
  }, 1000);
};

const Submit = () => {
  ref_form.value
    ?.validate()
    .then(async (valid) => {
      if (!valid) return;
      // 成功
      await updatePasswordAPI();
    })
    .catch((err: any) => {
      // 处理验证错误
      console.log("校验失败", err);
      ref_toast.value?.warning("表单验证失败！");
    });
};
/** 修改密码API */
const updatePasswordAPI = async () => {
  const form = list.value.form;
  const res = await findPassword({
    Mobile: form.Mobile,
    Password: form.Password,
    SmsCode: Number(form.SmsCode),
    ComfimPassword: form.confirmPassowrd,
  });
  const data = res.data;
  if (data && data?.Code != 1) {
    ref_toast.value?.fail(data.Message);
    return;
  }
  uni.showModal({
    title: "修改结果🦉",
    content: "密码已修改，点击下方去登录吧",
    showCancel: false,
    confirmText: "好的👌",
    success: () => {
      uni.redirectTo({
        url: "../login",
      });
    },
  });
  console.log("修改密码结果-->", data);
};
</script>

<style scoped>
.head {
  height: 170rpx;
  background-color: white;
  display: flex;
  place-items: center;
}

.head > view {
  margin-top: 40rpx;
}

.main {
  margin-top: 40rpx;
  width: 100%;
  height: 800rpx;
  /* border: 1px solid ; */
}

.item {
  width: 90%;
  height: 100rpx;
  padding: 10rpx 0rpx;
  margin: 35rpx auto;
  /* border: 1px solid gray; */
  border-bottom: 1px solid #e5e5e5;
  background-color: white;
  display: flex;
  color: #666666;
  place-items: center;
  position: relative;
}

/deep/ .u-input__content {
  font-size: 34rpx !important;
}

.item > view {
  width: 100%;
  padding: 20px;
  text-align: right;
}

.item > view > image {
  margin-top: 10rpx;
  width: 38rpx;
  height: 30rpx;
  /* border: 1px solid gray; */
}

.text {
  text-align: right;
  font-size: 20rpx;
  margin-right: 60rpx;
  padding-bottom: 10rpx;
  color: #888888;
  letter-spacing: 2rpx;
}

.input {
  letter-spacing: 3rpx;
}
</style>
<style>
/deep/ .u-form-item__body__right__message {
  position: absolute;
  top: 90rpx;
  text-align: left;
  width: auto !important;
  text-indent: 0px !important;
  /* border: 1px solid ; */
  margin-left: 120rpx !important;
  font-size: 24rpx !important;
  padding: 0px 30rpx;
  background-color: white;
}
</style>
