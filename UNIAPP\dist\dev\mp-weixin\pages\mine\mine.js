"use strict";
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_index = require("../../utils/index.js");
const utils_http = require("../../utils/http.js");
require("../../store/index.js");
require("../../utils/request.js");
require("../../store/pinia.js");
require("../../utils/sign.js");
require("../../utils/sha1.js");
if (!Math) {
  toast();
}
const toast = () => "../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "mine",
  setup(__props) {
    const ref_toast = common_vendor.ref();
    const userStore = store_user.useUserStore();
    const list = common_vendor.ref([
      {
        id: 3,
        icon: "../../static/mine/renshi.png",
        name: "人事专用",
        path: "../../pageA/pages/hr/hr"
      },
      // {
      //   id: 4,
      //   icon: "../../static/mine/riqi.png",
      //   name: "评分日期",
      //   path: "../../pageA/pages/hr/time",
      // },
      {
        id: 2,
        icon: "../../static/mine/icon3.png",
        name: "设置",
        path: "./views/setting"
      }
      // { id: 0, icon: '../../static/work/02.png', name: '历史评测', path: '../work/views/history' },
      // { id: 1, icon: '../../static/mine/icon1.png', name: '收到评价', path: './views/receiveComments' },
    ]);
    const toRouter = (url) => {
      common_vendor.index.navigateTo({
        url
      });
    };
    const getUserInfoAPI = async () => {
      var _a;
      const res = await utils_http.getUserInfo();
      const data = res.data.Data;
      if (data) {
        userStore.userInfo = data;
        console.log("用户信息", data);
        return;
      } else {
        (_a = ref_toast.value) == null ? void 0 : _a.fail(res.data.Message);
      }
    };
    const toUserInfo = () => common_vendor.index.navigateTo({ url: "../mine/views/userInfo" });
    common_vendor.onShow(() => {
      getUserInfoAPI();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.unref(utils_index.cantBeBad)(common_vendor.unref(userStore).userInfo.Avatar) || "../../static/images/avatar.png",
        b: common_vendor.o(($event) => toUserInfo()),
        c: common_vendor.t(common_vendor.unref(userStore).userInfo.DisplayName),
        d: common_vendor.o(($event) => toUserInfo()),
        e: common_vendor.t(common_vendor.unref(userStore).userInfo.Ex4),
        f: common_vendor.f(list.value, (item, index, i0) => {
          return {
            a: item.icon,
            b: common_vendor.t(item.name),
            c: item.id,
            d: common_vendor.o(($event) => toRouter(item.path), item.id)
          };
        }),
        g: common_vendor.sr(ref_toast, "d41d38da-0", {
          "k": "ref_toast"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d41d38da"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/mine/mine.vue"]]);
wx.createPage(MiniProgramPage);
