<template>
  <view class="basic-evaluation">
    <!-- <view class="inp" v-if="0">
      <up-input class="search" placeholder="请输入成员姓名" prefixIcon="search" @change="oninput"
        prefixIconStyle="font-size: 22px;color: #909399" :autoBlur="true" :focus="true" clearable
        v-model="searchValue"></up-input>
    </view> -->
    <view
      class="flex"
      v-if="historyStack.length > 1 && parentId != 0"
      @click="back()">
      <view class="shang">返回上一级</view>
      <view style="margin-right: 5%; max-height: 40rpx; padding: 10rpx 0px">
        <image
          style="max-height: 50rpx; max-width: 40rpx; margin-right: 5rpx"
          src="../../../static/index/back.png"
          mode="aspectFit"></image>
      </view>
    </view>

    <scroll-view scroll-y class="scroll-box">
      <user-list :list="list" :TIdNum="TIdNum" @select="handleSelect" />

      <view class="noData" v-show="!list.length && searchValue.length">
        <view class="noData-image">
          <image src="../../../static/index/noData.png" mode="aspectFit" />
        </view>
        <view class="noData-text">无数据哦</view>
      </view>
    </scroll-view>

    <toast ref="ref_toast"></toast>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useUserStore } from "@/store/user";
import { useIndexStore } from "@/store";
import UserList from "./components/UserList.vue";
import toast from "@/components/toast.vue";
import { getDepartmentInfo, searchUserList } from "@/utils/http";
import type { DepartmentUser } from "@/types";
import { onLoad, onShow } from "@dcloudio/uni-app";

const userStore = useUserStore();
const store = useIndexStore();
const ref_toast = ref();
const searchValue = ref("");
const list = ref<any>([]);
const TIdNum = ref<number>(store.submitData.TId);
const parentId = ref<string | number>(0);
const idx = ref(0);

// 历史记录栈
const historyStack = ref<Array<{ id: string | number; listData?: any[] }>>([
  { id: parentId.value },
]);

// 搜索功能
const oninput = async (UserName: string) => {
  list.value = [];
  if (UserName) {
    const res = await searchUserList({ UserName });
    list.value = res.data.Data.listjson;
  } else {
    getDepartmentInfoAPI(parentId.value);
  }
};

// 获取部门信息
const getDepartmentInfoAPI = async (id: string | number) => {
  const res = await getDepartmentInfo({
    ParentId: id,
    TId: TIdNum.value,
  });
  let data = res.data.Data.listjson;
  // console.log('list.value', list.value);
  uni.hideLoading();
  if (id === 0) {
    list.value = data;
    return;
  }
  list.value = data[idx.value]?.Users || [];
};

// 选择处理
const handleSelect = async (item: any, index: number) => {
  idx.value = index;
  if (TIdNum.value === 0) {
    getDepartmentInfoAPI(parentId.value);
    goEvaluation(item);
    return;
  }
  // console.log("item.DepartmentId:", item.DepartmentId);

  if (item.DepartmentId) {
    parentId.value = item.DepartmentId;
    historyStack.value.push({ id: parentId.value });
    getDepartmentInfoAPI(parentId.value);
  } else {
    goEvaluation(item);
  }
};

// 跳转评测
const goEvaluation = (item: DepartmentUser) => {
  if (item.DisplayName === userStore.userInfo.DisplayName) {
    uni.showToast({
      title: "无法在当前页面对自己评价哦",
      icon: "none",
    });
    return;
  }
  // console.log("item", item);

  store.routerUserinfo = item;
  if (TIdNum.value === 0) {
    store.routerUserinfo.DepartmentId = item.Id;
  } else {
    store.routerUserinfo.DepartmentId =
      historyStack.value[historyStack.value.length - 1].id;
  }

  uni.navigateTo({
    url: "/pages/index/views/assessment",
  });
};

// 返回上一级
const back = async () => {
  if (historyStack.value.length > 1) {
    historyStack.value.pop();
    const prevLevel = historyStack.value[historyStack.value.length - 1];
    parentId.value = prevLevel.id;
    await getDepartmentInfoAPI(prevLevel.id);
  }
};
onLoad(() => {
  getDepartmentInfoAPI(0);
});
onShow(() => {
  // getDepartmentInfoAPI(0)
});
</script>

<style lang="scss" scoped>
.basic-evaluation {
  height: calc(100vh - 13vh);
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  margin-top: 13vh;

  .shang {
    margin: 20rpx 0 20rpx auto;
    color: #666666;
  }

  .noData {
    text-align: center;
    padding: 40rpx;

    &-image {
      width: 90%;
      margin: 0 auto;

      image {
        width: 100%;
      }
    }

    &-text {
      margin-top: 20rpx;
      color: dodgerblue;
    }
  }
}
</style>
