<template>
	<view class="main">
		<view class="item" v-for="item,index in list" :key="index" style="border-bottom: 1px solid #E5E5E5;">
			<up-input class="input" type="text" v-model="item.value" border="none" :placeholder="item.text">
				<template #prefix>
					<view style="border-right:1rpx solid #E5E5E5;padding-right: 30rpx;display: flex;place-items: center;">
						<view class="">
							{{item.name}}
						</view>
						<view class=""  v-if="index != 0">
							<!-- <up-icon name="email" size="30"></up-icon> -->
						</view>
					</view>
				</template>
				<template #suffix v-if="index === 1">
					<view class="" style="margin-right: 30rpx;">
						<up-button type="primary" plain="" size="small" @click="getCode()">
							<view class="">
								{{codeText}}
							</view>
							<view class="" v-if="codeText === '获取验证码' || codeText === '重新发送'">
								<up-icon name="email" size="30" color="#2979ff"></up-icon>
							</view>
						</up-button>
					</view>
				</template>
			</up-input>
		</view>
		<view class="" style="margin-top: 10rpx;transition: all .5s;">
			<button type="primary" style="width: 80%;background: dodgerblue;" @click="onSubmit()">提交</button>
		</view>
		<!--  -->
	</view>
	<toast ref="ref_toast"></toast>
</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { useIndexStore } from '@/store';
	import toast from '@/components/toast.vue'
	import type { IToast } from '@/types';
	import { phoneRegex } from '@/utils';
	import { sendSms } from '@/utils/http';
	const ref_toast = ref<IToast>()
	const list = ref([
		{ name: '手机号',value:'',text:'请输入手机号',key:'' },
		{ name: '验证码',value:'',text:'请输入验证码',key:'' }, 
	])
	const props:any = defineProps({
		  config:{}
	})
	list.value = props.config
	const emits = defineEmits(['onSubmit'])
	console.log('父组件传递的数据 ---> ',props.config);
	const codeText = ref('获取验证码')
	const store = useIndexStore()
	// 函数
	const getCode = async()=>{
		if(codeText.value == '获取验证码' || codeText.value == '重新发送' ){
			await sendSms({Mobile:list.value[0].value,SmsType:'findphone'})
			.then(res=>{
				if(res.data.Code!=1){
					ref_toast.value?.info(res.data.Message)
					return
				}
				ref_toast.value?.success('发送成功')
				codeText.value = '已发送'
				timerFN(60)	
			})
		}else{
			ref_toast.value?.warning('请勿重复获取')
		}
	}
	const timerFN = (time = 60) =>{
		let timer = setInterval(()=>{
			time -= 1
			codeText.value = '已发送：' + time
			if(time <= 0){
				clearInterval(timer)
				codeText.value = '重新发送'
			}
		},1000)
	}
	const onSubmit = ()=>{
		if(!phoneRegex.test(list.value[0].value)){
			ref_toast.value?.warning('请填写正确的手机号')
			return
		}
		if(codeText.value === '获取验证码'){
			ref_toast.value?.warning('请点击获取验证码')
			return
		}
		if(!list.value[1].value){
			ref_toast.value?.warning('请填写验证码')
			return
		}
		uni.showLoading()
		const obj = {}
		list.value.map(item=>{
			obj[item.key] = item.value
		})
		emits('onSubmit',obj)
	}

</script>

<style scoped>
	.head{
		height: 170rpx;
		background-color: white;
		display: flex;
		place-items: center;
	}
	.head>view{
		margin-top: 40rpx;
	}
	.main {
		width: 100%;
		height: 800rpx;
		/* border: 1px solid ; */
	}
	
	.main>.item {
		width: 90%;
		height: 80rpx;
		padding: 10rpx 0rpx;
		margin: 25rpx auto;
		/* border: 1px solid gray; */
		background-color: white;
		display: flex;
		color: #666666;
		place-items: center;
	}
	
	.item>view {
		width: 50%;
		text-align: right;
		/* width: 100%;
		height: 100%;
		line-height: 60rpx;
		font-size: var(--size-4);
		letter-spacing: 2rpx;
		display: flex;
		justify-content: center;
		place-items: center; */
		/* border: 1px solid #ccc; */
	}
	
	.item>view>image {
		margin-top: 10rpx;
		width: 38rpx;
		height: 30rpx;
		/* border: 1px solid gray; */
	}
	.text{
		text-align: right;
		font-size: 20rpx;
		margin-right: 60rpx;
		padding-bottom: 10rpx;
		color: #888888;
		letter-spacing: 2rpx;
	}
	.input{
		letter-spacing: 3rpx;
	}
</style>