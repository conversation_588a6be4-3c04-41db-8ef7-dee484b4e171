<template>
	<view class="pageBox">
		<view class="head">
		    <view class="return" @click="returnSetp"> 
				<image class="return-img" src="../../../../static/icons/fanhui.png" ></image>
			</view>
		    <text class="title"></text>
		</view>
		<view class="">
			<image src="../../../../static/icons/ball.png" mode=""></image>
		</view>
		<view class="">
			正在审核中..
		</view>
	</view>
</template>

<script lang="ts" setup>
	// -> 导入-------------------
	
	import { ref } from 'vue';
	
		
	// -> 数据-------------------
	// -> 函数-------------------
	const returnSetp = ()=>{
		uni.navigateBack({
			delta: 1
		})
	}
</script>

<style lang="scss" scoped>
	.pageBox{
		background-color: white;
	}
</style>