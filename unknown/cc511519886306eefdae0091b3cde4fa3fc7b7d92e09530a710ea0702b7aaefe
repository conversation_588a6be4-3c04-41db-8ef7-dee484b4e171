"use strict";
const common_vendor = require("../../../../common/vendor.js");
const store_index = require("../../../../store/index.js");
if (!Math) {
  (Head + BasicEvaluation + CrossEvaluation)();
}
const Head = () => "../../../../components/head.js";
const BasicEvaluation = () => "./BasicEvaluation/index.js";
const CrossEvaluation = () => "./CrossEvaluation/index.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  props: {
    title: {
      type: String,
      default: "选择部门"
    }
  },
  emits: ["onReturn"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const store = store_index.useIndexStore();
    const TIdNum = common_vendor.ref(store.submitData.TId);
    const FuReturn = () => {
      emit("onReturn");
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(FuReturn),
        b: common_vendor.p({
          title: __props.title,
          returnType: "home"
        }),
        c: TIdNum.value <= 3
      }, TIdNum.value <= 3 ? {} : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/index/views/department/index.vue"]]);
wx.createPage(MiniProgramPage);
