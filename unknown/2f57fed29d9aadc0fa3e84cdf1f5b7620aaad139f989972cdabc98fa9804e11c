<template>
  <view class="pageBox">
    <view class="head">
      <view class="return" @click="store.backAPage()">
        <image
          class="return-img"
          src="../../../static/icons/fanhui.png"
          mode="aspectFit"></image>
      </view>
      <text class="title">选择</text>
    </view>
    <view class="main" v-if="list.length > 0">
      <view class="cu-list menu-avatar">
        <view
          class="cu-item padding-lg"
          v-for="(item, index) in list"
          :key="index"
          @click="enterThis(index)"
          :data-score="item.Score + '分'">
          <view class="score">{{ item.Score }}分</view>
          <view class="cu-avatar round lg" style="overflow: hidden">
            <image :src="onImg(item.Avatar)" mode="aspectFit"></image>
          </view>
          <view class="content">
            <view class="text-cyan">{{ isName(item) }}</view>
            <!-- <view class="text-cyan"> {{ toData.type == 1 ? item.DepartmentName : item.BserName || '泽维尔.' }}
						</view> -->
            <view
              class="text-sm"
              style="
                color: #888888;
                white-space: nowrap;
                text-overflow: ellipsis;
                max-width: 280px;
                overflow: hidden;
              ">
              <text>评价：{{ item.Reamrk }}</text>
            </view>
            <!-- <image image : src = "(cantBeBad(item.Avatar) || '../../../static/images/avatar.png')" mode = "aspectFit" > </image> -->
          </view>
          <view class="action">
            <view class="text-blue text-xs">
              {{ item.BepartmentName || "无" }}
            </view>
            <view class="text-sm">
              {{ item.CreateTime?.split(" ")[1].slice(0, 5) }}
            </view>
            <!-- <view style="margin-top: 10rpx;margin-left: 35rpx;" v-if="item?.Sex != 0">
							<up-icon name="man" size="4vw" 
							:color="'dodgerblue'" v-if="item.Sex===1"></up-icon>
							<up-icon name="woman" size="4vw"
							:color="'#ff557f'" :bold="true" v-if="item.Sex===2"></up-icon>
						</view>
						<view class="text-grey text-sm" v-else>
							{{item.Professional}}
						</view> -->
          </view>
        </view>
      </view>
    </view>
    <view class="noData" v-else>
      <view
        style="
          display: flex;
          flex-direction: column;
          width: 100%;
          padding: 20px;
          justify-content: center;
        ">
        <view class="slot-content" style="text-align: center">
          <image
            src="../../static/icons/nodata.png"
            mode="aspectFit"
            style="max-height: 320rpx"></image>
        </view>
        <view
          style="
            text-align: center;
            letter-spacing: 1px;
            color: #666666;
            margin-top: 10rpx;
          ">
          <button
            style="
              background: linear-gradient(80deg, #677bf0, #2330df);
              width: 50%;
            "
            @click="back()">
            暂无数据哦
          </button>
          <text style="color: #ff0079"></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { onLoad } from "@dcloudio/uni-app";
import { useIndexStore } from "@/store";
import { getAHistoryItem } from "@/utils/http";
import { ref, computed } from "vue";
import { cantBeBad } from "@/utils";
const store = useIndexStore();
const list = ref([]);
const toData = ref({
  index: 0,
  type: 1,
  date: "",
});
const onImg = (e) => {
  if (toData.value.type == 1) {
    return "../../../static/images/avatar.png";
  }
  return cantBeBad(e) || "../../../static/images/avatar.png";
};

const isName = (e) => {
  //互评
  if (toData.value.type == 1) {
    return e.DepartmentName;
  }
  //上对下
  if (toData.value.type == 2) {
    return e.UserName;
  }
  return e.BserName;
};
const back = () => {
  uni.navigateBack({ delta: 1 });
};
const enterThis = (index: number) => {
  toData.value.index = index;
  uni.navigateTo({
    url: "./tablePage?data=" + JSON.stringify(toData.value),
  });
};
/** 获取自评、互评，上对下，下对上数据 */
const getAHistoryItemAPI = async (TId: number, TimeDate: string) => {
  const res = await getAHistoryItem({ TId, TimeDate });
  const data = res.data?.Data?.listjson;
  if (!data) return;
  list.value = data;
  console.log(data);
};
onLoad((e) => {
  if (e.type && e.date) {
    toData.value.type = e.type;
    toData.value.date = e.date;
    getAHistoryItemAPI(e.type, e.date);
  }
});
</script>

<style scoped>
.main {
  padding-top: 0rpx;
}

.cu-item {
  position: relative;
  overflow: hidden;
}

.score {
  position: absolute;
  top: 12%;
  left: -3%;
  transform: rotateZ(-45deg);
  background-color: aquamarine;
  padding: 2rpx 30rpx;
  font-size: 24rpx;
  color: black;
  box-shadow: 2px 2px 2px #ccc;
  z-index: 1000;
  border-radius: 0px 0px 50px 0px;
}
</style>
