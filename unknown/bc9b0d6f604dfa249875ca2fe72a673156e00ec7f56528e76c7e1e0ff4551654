import { defineStore } from 'pinia'
import { reactive } from 'vue'
import type { DepartmentUser, SetAppraisal } from '@/types'

export const useIndexStore = defineStore('index', {
	// state是一个函数，返回一个对象
	state: () => {
		return {
			isWx:true,
			// 选择部门人员后得到的被评人信息
			routerUserinfo: reactive<DepartmentUser>({
				ID: 1,
				DisplayName: '未知用户',
				Sex: 1,
				Ex4: '销售部',
				Avatar: ''
			}),
			//提交页面的所需数据
			submitData: reactive<SetAppraisal>({
				TId: 0,
				BId: 0,
				Content: [],
				Score: '0',
				Reamrk: '',
				ExtraScore: ''
			}),
			backAPage: () => {
				uni.navigateBack({
					delta: 1
				})
			},
			resetData:function (){
				this.submitData = {
					TId: 0,
					BId: 0,
					Content: [],
					Score: '0',
					Reamrk: '',
					ExtraScore: ''
				}
			}
		}
	},
	getters: {

	},
	actions: {},
}
)