<template>
	<view class="pageBox">
		<view class="head">
			<view class="return" @click="store.backAPage()">
				<image class="return-img" src="../../../static/icons/fanhui.png" mode="aspectFit"></image>
			</view>
			<text class="title">评测表格</text>
		</view>
		<view class="tableBox">
			<view class="tableTitle">
				<view>
					姓名：{{ formData?.UserName }}
				</view>
				<view>
					部门：{{ formData?.DepartmentName || '无' }}
				</view>
				<view>
					时间：{{ formData?.CreateTime?.split(' ')[0] || '未知日期' }}
				</view>
			</view>

			<view class="tableContent">
				<view class="tableItem tableHeader">
					<view class="cell1">
						考评要素
					</view>
					<view class="cellBox">
						<view class="cellContent">
							<view class="cell2 first">
								考评参考
							</view>
							<view class="cell3">
								考评评分
							</view>
						</view>
					</view>
					<view class="cell4">
						合计
					</view>
				</view>
				<view class="tableItem" v-for="item1 in formData?.Content" :key="item1">
					<view class="cell1">
						{{ item1.Name }} ({{ item1.Weights }}分)
					</view>
					<view class="cellBox">
						<view class="cellContent" v-for="item2, index2 in item1?.List0" :key="item2">
							<view class="cell2">
								{{ index2 + 1 }}.{{ item2.Content }} ({{ item2.Weight + '%' }})
							</view>
							<view class="cell3">
								{{ item2.Fraction }}
							</view>
						</view>
					</view>
					<view class="cell4">
						{{ onFen(item1) }}
					</view>
				</view>
			</view>
			<!-- 综合评分 -->
			<view class="synthesize">
				<view class="synthesize-left">
					综合评分
				</view>
				<view class="synthesize-middle">
					（满分为100分，60分为及格）
				</view>
				<view class="synthesize-right">
					{{ formData.Score }}
				</view>
			</view>
			<!-- 自我评价 -->
			<view class="self">
				<view class="self-left">
					评价内容
				</view>
				<view class="self-right">
					<textarea class="textarea" v-model="text" :data-currentCount="text.length" :data-maxlength="'/' + 1000"
						maxlength="1000" disabled></textarea>
				</view>
			</view>

		</view>

		<view class="bug">

		</view>
	</view>

</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useIndexStore } from '@/store';
import { onLoad } from '@dcloudio/uni-app';
import { getAHistoryItem } from '@/utils/http';
import { checkLogin } from '@/utils/request';
const store = useIndexStore()
const text = ref(``)
const formData = ref({})

/** 计算小题得分 */
const onFen = (item: { Weights: number; List0: Array<{ Weight: number; Fraction: number; xiaofen?: number }> }) => {
	if (!item || !item.List0 || !Array.isArray(item.List0) || item.List0.length === 0) {
		return '0.00'
	}
	const totalWeight = item.Weights // 大题总分值
	const totalScore = item.List0.reduce((sum, subItem) => {
		const subScore = totalWeight * (subItem.Weight / 100) * (subItem.Fraction / 5)
		if (typeof subItem === 'object') {
			subItem.xiaofen = subScore
		}
		return sum + subScore
	}, 0)

	// 返回保留两位小数的结果
	return totalScore.toFixed(2)
}

/** 获取自评、数据 */
const getAHistoryItemAPI = async (TId: number, TimeDate: string) => {
	const res = await getAHistoryItem({ TId, TimeDate })
	const data = res.data?.Data?.listjson[0]
	if (!data) {
		uni.showToast({
			icon: 'none',
			title: '无数据',
			duration: 3000
		})
		setTimeout(() => {
			uni.navigateBack({ delta: 1 })
		}, 2000)
		return
	};
	formData.value = data
	formData.value.Content = formData.value.Content
	text.value = data?.Reamrk || ''
	console.log(data);

}
/** 获取互评，上对下，下对上数据 */
const getAHistoryItemAPI2 = async (index: number, TId: number, TimeDate: string) => {
	const res = await getAHistoryItem({ TId, TimeDate })
	const data = res.data?.Data?.listjson[index]
	if (!data) return;
	formData.value = data
	formData.value.UserName = data?.BserName
	formData.value.DepartmentName = data?.BepartmentName
	formData.value.Content = formData.value.Content
	text.value = text.value = data?.Reamrk || ''
	// console.log(formData.value.Content,data);
}
onLoad((e: any) => {
	// 自评
	if (e.type && e.date) {
		getAHistoryItemAPI(e.type, e.date)
		console.log('自评', e);
	}
	// 除自评以外的
	if (e.data) {
		const data = JSON.parse(e.data)
		console.log('除自评以外的', data);
		getAHistoryItemAPI2(data.index, data.type, data.date)
	}

})
</script>

<style scoped lang="scss">
.pageBox {
	overflow: auto !important;
}

.tableBox {
	width: 100%;
	min-height: 80vh;
	border-top: 1px solid #EEF1FE;
	border-bottom: 1px solid #EEF1FE;
	padding: 0 20rpx;
	font-size: 12px;
	color: #666666;
}

.tableTitle {
	padding: 30rpx 15rpx;
	display: flex;
	justify-content: space-between;
	font-size: 27rpx !important;

}

.tableContent {
	width: 100%;
}

.tableItem {
	display: flex;
}

.tableItem:nth-child(odd) {
	background-color: #F8F8FF;
}

.synthesize {
	display: flex;
	font-size: 12px;
	align-items: center;
	justify-content: center;
	background-color: #F8F8FF;
}

.synthesize-left {
	flex: 2;
	width: 170rpx;
	padding: 8px 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #EEF1FE;
	box-sizing: border-box;
	border-bottom: 1px solid transparent;
	border-right: 1px solid transparent;
}

.synthesize-middle {
	flex: 7;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
	// background-color: #bfc;
	max-width: 486rpx;
	border-top: 1px solid #EEF1FE;
	border-left: 1px solid #EEF1FE;
	padding: 9px 4px 8px;
	text-align: center;
}

.synthesize-right {
	flex: 1;
	width: 120rpx;
	text-align: center;
	padding: 8px 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #EEF1FE;
	box-sizing: border-box;
	border-bottom: 1px solid transparent;

}

.self {
	display: flex;
}

.self-left {
	flex: 2;
	max-width: 152rpx;
	padding: 8px 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #EEF1FE;
	box-sizing: border-box;
	border-right: 1px solid transparent;
}

.self-right {
	width: 610rpx;
	flex: 8;
	border: 1px solid #EEF1FE;


}

.cell1 {
	flex: 2;
	width: 170rpx;
	padding: 8px 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #EEF1FE;
	box-sizing: border-box;
	border-bottom: 1px solid transparent;
	border-right: 1px solid transparent;
}

.cellBox {
	flex: 7;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}

.first {
	text-align: center;
}

.cell2 {
	flex: 5;
	width: 352rpx;
	padding: 8px 4px;
	border: 1px solid #EEF1FE;
	box-sizing: border-box;
	border-bottom: 1px solid transparent;
	border-right: 1px solid transparent;
	text-indent: 2px;
}


.cell3 {
	flex: 2;
	width: 124rpx;
	text-align: center;
	padding: 8px 4px;
	border: 1px solid #EEF1FE;
	box-sizing: border-box;
	border-bottom: 1px solid transparent;
	border-right: 1px solid transparent;
}

//.cellContent .cell3:last-child {
// 	border-bottom: 1px solid #e03e3e;
// }

.cell4 {
	flex: 1;
	width: 120rpx;
	text-align: center;
	padding: 8px 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #EEF1FE;
	box-sizing: border-box;
	border-bottom: 1px solid transparent;
}

.cellContent {
	width: 100%;
	display: flex;
	align-items: stretch;
	box-sizing: border-box;
}

.tableHeader {
	background-color: #F8F8FF;
}

.textarea {
	width: 100%;
	background-color: white;
	border-radius: 5px;
	border: none;
	color: #666666;
	letter-spacing: 1rpx;
	text-align: left;
	font-size: 25rpx;
}

.textarea:before {
	content: attr(data-currentCount);
	position: absolute;
	right: 90rpx;
	bottom: 5px;
	color: #ccc;
}

.textarea::after {
	content: attr(data-maxLength);
	position: absolute;
	right: 5px;
	bottom: 5px;
	color: #666666;
}
</style>