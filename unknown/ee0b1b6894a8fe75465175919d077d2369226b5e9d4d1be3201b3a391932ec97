export interface IToast {
  //通知消息
  info: (text: string, time?: number, icon?: "none" | any) => void;
  warning: (
    text: string,
    time?: number,
    icon?: "checkbox-mark" | "none" | any
  ) => void;
  success: (
    text: string,
    time?: number,
    icon?: "arrow-up-fill" | "none" | any
  ) => void;
  fail: (text: string, time?: number, icon?: "error" | "none" | any) => void;
  netFail: (
    text: string,
    time?: number,
    icon?: "wifi-off" | "none" | any
  ) => void;
  // 加载
  // loading:(options: ILoadingOptions) => Promise<{ close: ()=>void}>
}

export interface DepartmentUser {
  ID: number;
  Ex4: string;
  DisplayName: string;
  Avatar: string | null;
  Sex: number;
  Mobile?: string;
}
export interface DepartmentInfo {
  Id: number;
  Name: string;
  List?: DepartmentUser[];
}
/** 部门结构 接口返回 */
export type IDepartment = DepartmentInfo[];

export interface QuestionData {
  Id: number;
  value: number;
  Content: string;
  Fraction?: number; // 小题占比
}
export interface QuestionTitle {
  Id: number;
  Name: string;
  List: QuestionData[];
  Weights: number;
  totalValue?: number;
}
/** 问题 接口返回 */
export type IQuestion = QuestionTitle[];

export interface QuestionData2 {
  Id: number;
  Value: string;
}
/** 提交评测表单 */
export interface SetAppraisal {
  TId: number; //评测类型 0 -3
  BId: number; // 被评测人的id 不满足就传个0
  Content: QuestionData2[];
  Score: string;
  Reamrk: string;
  ExtraScore: string;
}
// 添加 Deadline 接口定义
export interface Deadline {
  SubmitDay: number; // 提出跨部门打分申请的开始日期
  AssignDay: number; // 主管审核/指定跨部门打分的截止日期
  UrgedDay: number; // 人事催促打分截止日期
  CompleteDay: number; // 停止打分日期
}
