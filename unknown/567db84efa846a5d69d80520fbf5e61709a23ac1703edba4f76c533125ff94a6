"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const utils_http = require("../../../utils/http.js");
const utils_index = require("../../../utils/index.js");
require("../../../utils/request.js");
require("../../../store/user.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "middlePage",
  setup(__props) {
    const store = store_index.useIndexStore();
    const list = common_vendor.ref([]);
    const toData = common_vendor.ref({
      index: 0,
      type: 1,
      date: ""
    });
    const onImg = (e) => {
      if (toData.value.type == 1) {
        return "../../../static/images/avatar.png";
      }
      return utils_index.cantBeBad(e) || "../../../static/images/avatar.png";
    };
    const isName = (e) => {
      if (toData.value.type == 1) {
        return e.DepartmentName;
      }
      if (toData.value.type == 2) {
        return e.UserName;
      }
      return e.BserName;
    };
    const back = () => {
      common_vendor.index.navigateBack({ delta: 1 });
    };
    const enterThis = (index) => {
      toData.value.index = index;
      common_vendor.index.navigateTo({
        url: "./tablePage?data=" + JSON.stringify(toData.value)
      });
    };
    const getAHistoryItemAPI = async (TId, TimeDate) => {
      var _a, _b;
      const res = await utils_http.getAHistoryItem({ TId, TimeDate });
      const data = (_b = (_a = res.data) == null ? void 0 : _a.Data) == null ? void 0 : _b.listjson;
      if (!data)
        return;
      list.value = data;
      console.log(data);
    };
    common_vendor.onLoad((e) => {
      if (e.type && e.date) {
        toData.value.type = e.type;
        toData.value.date = e.date;
        getAHistoryItemAPI(e.type, e.date);
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(($event) => common_vendor.unref(store).backAPage()),
        b: list.value.length > 0
      }, list.value.length > 0 ? {
        c: common_vendor.f(list.value, (item, index, i0) => {
          var _a;
          return {
            a: common_vendor.t(item.Score),
            b: onImg(item.Avatar),
            c: common_vendor.t(isName(item)),
            d: common_vendor.t(item.Reamrk),
            e: common_vendor.t(item.BepartmentName || "无"),
            f: common_vendor.t((_a = item.CreateTime) == null ? void 0 : _a.split(" ")[1].slice(0, 5)),
            g: index,
            h: common_vendor.o(($event) => enterThis(index), index),
            i: item.Score + "分"
          };
        })
      } : {
        d: common_vendor.o(($event) => back())
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-fbaab64a"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/work/views/middlePage.vue"]]);
wx.createPage(MiniProgramPage);
