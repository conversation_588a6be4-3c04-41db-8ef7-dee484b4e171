"use strict";
const common_vendor = require("../../common/vendor.js");
const store_index = require("../../store/index.js");
const utils_index = require("../../utils/index.js");
const utils_http = require("../../utils/http.js");
require("../../utils/request.js");
require("../../store/user.js");
require("../../store/pinia.js");
require("../../utils/sign.js");
require("../../utils/sha1.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  const _easycom_up_input2 = common_vendor.resolveComponent("up-input");
  (_easycom_up_icon2 + _easycom_up_button2 + _easycom_up_input2)();
}
const _easycom_up_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_button = () => "../../node-modules/uview-plus/components/u-button/u-button.js";
const _easycom_up_input = () => "../../node-modules/uview-plus/components/u-input/u-input.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_button + _easycom_up_input + toast)();
}
const toast = () => "../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "validatePhonePage",
  props: {
    config: {}
  },
  emits: ["onSubmit"],
  setup(__props, { emit: __emit }) {
    const ref_toast = common_vendor.ref();
    const list = common_vendor.ref([
      { name: "手机号", value: "", text: "请输入手机号", key: "" },
      { name: "验证码", value: "", text: "请输入验证码", key: "" }
    ]);
    const props = __props;
    list.value = props.config;
    const emits = __emit;
    console.log("父组件传递的数据 ---> ", props.config);
    const codeText = common_vendor.ref("获取验证码");
    store_index.useIndexStore();
    const getCode = async () => {
      var _a;
      if (codeText.value == "获取验证码" || codeText.value == "重新发送") {
        await utils_http.sendSms({ Mobile: list.value[0].value, SmsType: "findphone" }).then((res) => {
          var _a2, _b;
          if (res.data.Code != 1) {
            (_a2 = ref_toast.value) == null ? void 0 : _a2.info(res.data.Message);
            return;
          }
          (_b = ref_toast.value) == null ? void 0 : _b.success("发送成功");
          codeText.value = "已发送";
          timerFN(60);
        });
      } else {
        (_a = ref_toast.value) == null ? void 0 : _a.warning("请勿重复获取");
      }
    };
    const timerFN = (time = 60) => {
      let timer = setInterval(() => {
        time -= 1;
        codeText.value = "已发送：" + time;
        if (time <= 0) {
          clearInterval(timer);
          codeText.value = "重新发送";
        }
      }, 1e3);
    };
    const onSubmit = () => {
      var _a, _b, _c;
      if (!utils_index.phoneRegex.test(list.value[0].value)) {
        (_a = ref_toast.value) == null ? void 0 : _a.warning("请填写正确的手机号");
        return;
      }
      if (codeText.value === "获取验证码") {
        (_b = ref_toast.value) == null ? void 0 : _b.warning("请点击获取验证码");
        return;
      }
      if (!list.value[1].value) {
        (_c = ref_toast.value) == null ? void 0 : _c.warning("请填写验证码");
        return;
      }
      common_vendor.index.showLoading();
      const obj = {};
      list.value.map((item) => {
        obj[item.key] = item.value;
      });
      emits("onSubmit", obj);
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(list.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.name),
            b: index != 0
          }, index != 0 ? {} : {}, {
            c: index === 1
          }, index === 1 ? common_vendor.e({
            d: common_vendor.t(codeText.value),
            e: codeText.value === "获取验证码" || codeText.value === "重新发送"
          }, codeText.value === "获取验证码" || codeText.value === "重新发送" ? {
            f: "b4c91cf8-2-" + i0 + "," + ("b4c91cf8-1-" + i0),
            g: common_vendor.p({
              name: "email",
              size: "30",
              color: "#2979ff"
            })
          } : {}, {
            h: common_vendor.o(($event) => getCode(), index),
            i: "b4c91cf8-1-" + i0 + "," + ("b4c91cf8-0-" + i0),
            j: common_vendor.p({
              type: "primary",
              plain: "",
              size: "small"
            })
          }) : {}, {
            k: "b4c91cf8-0-" + i0,
            l: common_vendor.o(($event) => item.value = $event, index),
            m: common_vendor.p({
              type: "text",
              border: "none",
              placeholder: item.text,
              modelValue: item.value
            }),
            n: index
          });
        }),
        b: common_vendor.o(($event) => onSubmit()),
        c: common_vendor.sr(ref_toast, "b4c91cf8-3", {
          "k": "ref_toast"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b4c91cf8"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/componentPage/validatePhonePage.vue"]]);
wx.createPage(MiniProgramPage);
