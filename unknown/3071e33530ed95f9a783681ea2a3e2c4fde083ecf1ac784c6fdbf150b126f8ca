
.pageBox.data-v-83a5a03c {
  padding: 15rpx;
}
.titleBox.data-v-83a5a03c {
  width: 100%;
  padding: 20rpx;
  margin-top: 10px;
}
.functionBox.data-v-83a5a03c {
  margin-top: 30rpx;
  width: 100%;
  padding: 20rpx;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 40rpx;
}
.icon.data-v-83a5a03c {
  width: 50rpx;
  height: 50rpx;
}
.card.data-v-83a5a03c {
  width: 42vw;
  height: 155rpx;
  background: var(--background-login);
  border-radius: 6px;
  font-size: var(--size-3);
  box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0, 0, 0, 0.2);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 26rpx;
}
.cardImage.data-v-83a5a03c {
  width: 80rpx;
  height: 80rpx;
  padding-left: 0rpx;
}
.cardImage.data-v-83a5a03c:last-child {
  border-radius: 50%;
}
.mask.data-v-83a5a03c {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.noticeBox.data-v-83a5a03c {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}
.data-v-83a5a03c .u-icon__icon {
  font-size: 14px !important;
}
.data-v-83a5a03c .u-notice__swiper__item__text {
  font-size: 14px !important;
}
.yellow.data-v-83a5a03c {
  background-color: #fdf6ec;
  height: 34px;
  line-height: 34px;
  padding-left: 20rpx;
  font-weight: bold;
  color: red;
  font-size: 14px;
}
