"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "checking",
  setup(__props) {
    const returnSetp = () => {
      common_vendor.index.navigateBack({
        delta: 1
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(returnSetp)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a3e373cf"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/login/views/acount/checking.vue"]]);
wx.createPage(MiniProgramPage);
