<template>
  <view class="pageBox">
    <view class="head">
      <view class="return" @click="store.backAPage()">
        <image
          class="return-img"
          src="../../../static/icons/fanhui.png"
          mode="aspectFit"></image>
      </view>
      <text class="title">提交</text>
    </view>
    <view class="content1"></view>
    <view class="content2"></view>
    <view class="content">
      <view class="contentHead">
        <view style="display: flex">
          {{
            userInfo.DisplayName != userStore.userInfo.DisplayName
              ? "被评者"
              : "姓名"
          }}：{{ userInfo.DisplayName }}
          <view style="margin-right: 30rpx"></view>
          <view class="overText" style="white-space: nowrap">
            职位：
            <text class="overText" style="white-space: nowrap">
              {{ userInfo.Ex4 || "无" }}
            </text>
          </view>
          <!-- 性别：<view class="" style="margin-left: -30rpx;margin-top: 6rpx;">
						<up-icon name="man" size="2vh"
							:color="'dodgerblue'" v-if="userInfo.Sex === 1"></up-icon>
						<up-icon name="woman" size="1.6vh"
							:color="'#ff557f'" :bold="true" v-else></up-icon>
					</view> -->
        </view>
        <view style="color: #888888">
          <!-- 部门 -->
          {{ userInfo.Ex4 }}
        </view>
        <view style="line-height: 42rpx; color: #888888">
          <text class="time">{{ getCurrentDate() }}</text>
          <text class="score">分数：{{ store.submitData.Score }}</text>
        </view>

        <image
          class="tx"
          style="border-radius: 50%"
          :src="
            cantBeBad(userInfo.Avatar) || '../../../static/images/avatar.png'
          "
          mode="aspectFill"></image>
      </view>
      <view class="contentTitle">
        <view>最后评价</view>
      </view>
      <view class="questionBox">
        <view style="padding: 30rpx" v-if="admin">
          额外评分
          <text class="maybe">（选填）</text>
        </view>
        <view
          v-if="admin"
          style="background-color: white; width: 94.5%; margin-left: 20rpx">
          <input
            type="digit"
            class="_input"
            placeholder="请输入额外加分或减分（0-100）"
            v-model="formData.ExtraScore"
            placeholder-style="color:#888888;letter-spacing: 1px;line-height: 70rpx;font-size: 26rpx;text-indent:10rpx;" />
        </view>
        <view style="padding: 30rpx">
          评价内容
          <text class="maybe">（选填）</text>
        </view>
        <textarea
          class="textarea"
          v-model="formData.Reamrk"
          :data-currentCount="formData.Reamrk.length"
          :data-maxlength="'/' + 1000"
          maxlength="1000"
          placeholder="请输入内容"></textarea>
      </view>
      <view class="tips">
        <view>请各部门、各同事单独填写此评价表，不要和任何人进行讨论；</view>
        <view>
          本次评估由公司汇总保密，不会像被评估人反馈，请各位如实填写；
        </view>
      </view>
    </view>
    <view class="bottom flex">
      <text style="letter-spacing: 1rpx; text-indent: 40rpx">
        共 {{ tiBig }} 大题 共 {{ tiSmall }} 小题
      </text>
      <view @click="submit()" class="submitText">提交</view>
    </view>

    <toast ref="ref_toast"></toast>

    <up-modal
      :show="showConfirm"
      :title="'提交结果'"
      :confirmText="
        modelText1 === '感谢您完成本月度的评测提交' ? '好的' : ' 行 吧 💔'
      "
      @confirm="closeConfirm">
      <view style="display: flex; flex-direction: column">
        <view class="slot-content">
          <image
            src="../../static/index/ok2.png"
            mode="aspectFit"
            style="max-height: 320rpx"
            v-if="modelText1 === '感谢您完成本月度的评测提交'"></image>
          <image
            src="../../static/index/sleep.png"
            mode="aspectFit"
            style="max-height: 320rpx"
            v-else></image>
        </view>
        <view
          style="
            text-align: center;
            letter-spacing: 1px;
            color: #666666;
            margin-top: 10rpx;
          ">
          {{ modelText1 }}
          <text
            style="color: #ff0079"
            v-if="modelText1 === '感谢您完成本月度的评测提交'">
            ❤
          </text>
        </view>
      </view>
    </up-modal>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useIndexStore } from "@/store";
import { getCurrentDate } from "@/utils";
import toast from "../../../components/toast.vue";
import type { IToast, SetAppraisal } from "@/types";
import { useUserStore } from "@/store/user";
import { setAppraisal } from "@/utils/http";
import { cantBeBad } from "@/utils";
import { onLoad, onShow } from "@dcloudio/uni-app";
const ref_toast = ref<IToast>();
const store = useIndexStore();
const userStore = useUserStore();
const userInfo = ref(store.routerUserinfo);
const admin = userStore.IsDepartmentlanager;
const showConfirm = ref(false);
const modelText1 = ref("感谢您完成本月度的评测提交");
const tiBig = ref(0);
const tiSmall = ref(0);
onLoad((e: any) => {
  // console.log("e :>> ", e);
  if (e && Object.keys(e).length > 0) {
    tiBig.value = e.tiBig;
    tiSmall.value = e.tiSmall;
  }
});
if (store.submitData.TId == 0) {
  //当前登录用户的个人信息
  userInfo.value = userStore.userInfo;
} else {
  //选择部门后的人信息
  userInfo.value = store.routerUserinfo;
  console.log(" id --->", store.submitData.BId, userInfo.value?.ID);
  store.submitData.BId = userInfo.value?.ID;
}
if (userInfo.value.Avatar?.length === 0 || !userInfo.value.Avatar) {
  //默认头像
  userInfo.value.Avatar = "../../../static/images/avatar.png";
}
const formData = ref<SetAppraisal>({
  TId: store.submitData.TId,
  BId: store.submitData.BId,
  Content: JSON.stringify(store.submitData.Content),
  Score: store.submitData.Score,
  Reamrk: "",
  ExtraScore: "",
  DepartmentId: store.routerUserinfo.DepartmentId,
});
const closeConfirm = () => {
  showConfirm.value = false;
  uni.navigateBack({
    delta: 10,
  });
};
// const beforeSubmit = ()=>{
// 	uni.showModal({
// 		title:'本次打分为:' + formData.value.Score + '分',
// 		content:`请您确认无误后提交本次打分`,
// 		success:function(e){
// 			if(e.confirm){
// 				submit()
// 			}
// 		}
// 	})
// }
/** 点击返回 */

const submit = async function () {
  const len = formData.value.Reamrk.length;
  const len2 = formData.value.ExtraScore.length;
  if (len && !len2 && admin) {
    ref_toast.value?.warning("请先评分");
    return;
  }

  uni.showLoading({
    title: "数据提交中...",
  });
  if (Number(formData.value.Score) == 0) {
    ref_toast.value?.fail("抱歉，成绩不能为0分");
    return;
  }
  await setAppraisalAPI();
  uni.hideLoading();
};
/** 提交接口 */
const setAppraisalAPI = async () => {
  const res = await setAppraisal(formData.value);
  if (!res) return;
  const data = res.data;
  uni.hideLoading();
  console.log("提交结果", data);
  if (res.data?.Code === 2) {
    modelText1.value = res.data.Message;
  }
  showConfirm.value = true;
  return data;
};

onShow(() => {
  uni.setStorageSync("jianyi", "suibian");
});
</script>

<style scoped>
.pageBox {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.content {
  margin: auto;
  width: 92%;
  height: 75%;
  border-radius: 25rpx;
  z-index: 3;
  background-color: white;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(4, 0, 0, 0.12);
}

.content1 {
  position: absolute;
  top: 12.7%;
  left: 5%;
  width: 90%;
  height: 75%;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(4, 0, 0, 0.1);
  border-radius: 25rpx;
  z-index: 2;
}

.content2 {
  position: absolute;
  top: 11.7%;
  left: 6%;
  width: 88%;
  height: 75%;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(4, 0, 0, 0.08);
  border-radius: 25rpx;
  z-index: 1;
}

.contentHead {
  display: flex;
  flex-direction: column;
  justify-content: right;
  font-size: 30rpx;
  padding: 20rpx 10rpx 0rpx 10rpx;
  text-indent: 10px;
}

.contentHead > view:nth-child(n + 2) {
  padding: 5rpx 0rpx;
}

.contentTitle {
  width: 100%;
  text-indent: 30rpx;
  font-size: 34rpx;
  line-height: 70rpx;
  height: 80rpx;
  letter-spacing: 1rpx;
  position: relative;
}

.contentTitle > image {
  margin-left: auto;
  margin-right: 40rpx;
  height: 120rpx;
  width: 120rpx;
  border-radius: 10px;
}

.contentTitle::before {
  position: absolute;
  content: "";
  left: 5%;
  bottom: 5%;
  width: 60rpx;
  height: 10rpx;
  background: linear-gradient(to right, dodgerblue 5%, blue);
  box-shadow: 0 0 2px #ccc;
  border-radius: 10px;
}

.questionContainer {
  margin-top: 30rpx;
  width: 92%;
  margin-left: 4%;
  height: 66%;
  padding: 10rpx 15rpx;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 3;
}

.tx {
  position: absolute;
  margin-top: 35rpx;
  right: 70rpx;
  width: 20vw;
  height: 20vw;
  box-shadow: 1px 0 4px rgba(240, 240, 240, 1);
}

.questionBox {
  margin-top: 30rpx;
  width: 92%;
  margin-left: 4%;
  height: 66%;
  padding: 10rpx;
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 3;
}

.tips {
  margin-top: 30rpx;
  width: 100%;
  padding: 10rpx;
  border-radius: 10px;
  z-index: 3;
  color: #666666;
  white-space: nowrap;
}

.tips > view:nth-child(1) {
  font-size: 22rpx;
  text-align: center;
}

.tips > view:nth-child(2) {
  margin-top: 10rpx;
  font-size: 23rpx;
  text-align: center;
}

.textarea {
  width: 96%;
  margin-left: 2%;
  background-color: white;
  padding: 20rpx 20rpx 40rpx 20rpx;
  min-height: 50%;
  border-radius: 5px;
  border: none;
  color: #666666;
  letter-spacing: 1rpx;
}

.textarea:before {
  content: attr(data-currentCount);
  position: absolute;
  right: 106rpx;
  bottom: 5px;
  color: #ccc;
}

.textarea::after {
  content: attr(data-maxLength);
  position: absolute;
  right: 5px;
  bottom: 5px;
  color: #666666;
}

.bottom {
  margin-top: 70rpx;
  width: 100%;
  border-top: 1px solid rgba(240, 240, 240, 1);
  color: #666666;
}

.bottom > view {
  padding: 40rpx;
}

.submitText {
  margin-left: auto;
  width: 180rpx;
  font-size: 36rpx;
  text-align: center;
  background-color: #f7f7f7;
}

._input {
  padding: 10rpx;
  height: 70rpx;
  line-height: 70px;
  border-radius: 5px;
}

.maybe {
  color: #888888;
  font-size: 26rpx;
  letter-spacing: 1px;
}

.time {
  margin-right: 56rpx;
}

.score {
  font-weight: bold;
  color: black;
}
</style>
