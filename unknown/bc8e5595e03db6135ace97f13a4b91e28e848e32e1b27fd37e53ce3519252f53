<template>
  <view class="pageBox">
    <view class="head">
      <view class="return" @click="returnSetp">
        <image
          class="return-img"
          src="../../../static/icons/fanhui.png"></image>
      </view>
      <text class="title">用户注册</text>
    </view>
    <view class="main">
      <up-form
        labelPosition="left"
        :model="model"
        :rules="model.rules"
        ref="ref_form">
        <up-form-item prop="form.username">
          <view class="cu-form-group inputBox" data-label="手机号">
            <view class="title">
              <button
                class="cu-btn round sm"
                style="
                  font-size: var(--size-2);
                  letter-spacing: 2rpx;
                  color: var(--bk-3);
                  background-color: transparent;
                ">
                {{ list[0].name }}
              </button>
            </view>
            <input
              placeholder="请输入账号"
              name="username"
              placeholder-style="font-size:26rpx;letter-spacing: 6rpx;" />
          </view>
        </up-form-item>

        <up-form-item prop="form.verifyCode">
          <view class="cu-form-group inputBox" data-label="验证码">
            <input
              placeholder="请输入手机验证码"
              name="verifyCode"
              placeholder-style="font-size:26rpx;letter-spacing: 6rpx;" />
            <text
              class="text-blue verifyCode"
              style="color: var(--blue-2); font-size: var(--size-2)"
              @click="getCode()">
              {{ codeText }}
            </text>
          </view>
        </up-form-item>

        <up-form-item prop="form.password">
          <view class="cu-form-group inputBox" data-label="密码">
            <input
              type="password"
              placeholder="请输入密码"
              name="password"
              placeholder-style="font-size:26rpx;letter-spacing: 6rpx;" />
          </view>
        </up-form-item>
      </up-form>

      <view class="cu-form-group" style="border: none">
        <button class="submit" @click="register()">注册</button>
      </view>

      <view class="fnBox1" style="text-align: center; text-indent: 90rpx">
        还未有账号 ?
        <text style="color: #5266e2" @click="toLogin()">去登录</text>
      </view>
    </view>
    <!-- 弹窗组件 -->
    <toast ref="ref_toast"></toast>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
// import { useUserStore } from '@/store/store'
import toast from "@/components/toast.vue";
// 数据
const ref_toast = ref<InstanceType<typeof toast>>();
const ref_form = ref<HTMLElement | null>(null)!;
// 使用 ref 创建响应式数据
const list = ref([
  {
    name: "+86 (中国)",
    color: "#ffaa7f",
    fontSize: "20",
  },
  {
    name: "选项一",
    subname: "选项一描述",
    color: "#ffaa7f",
    fontSize: "20",
  },
]);
const model = ref({
  form: {
    username: "",
    password: "",
    verifyCode: "",
  },
  rules: {
    "form.username": {
      type: "string",
      required: true,
      message: "请输入手机号",
      trigger: ["blur", "change"],
    },
    "form.verifyCode": {
      type: "string",
      required: true,
      message: "请输入手机验证码",
      trigger: ["blur", "change"],
    },
    "form.password": {
      type: "string",
      required: true,
      message: "请输入密码",
      trigger: ["blur", "change"],
    },
  },
});
const store = {};
let codeText = ref<number | string>("获取验证码");

// 函数
const getCode = () => {
  if (typeof codeText.value == "string") {
    ref_toast.value?.success("发送成功");
    codeText.value = "已发送";
    timerFN(60);
  } else {
    ref_toast.value?.warning("请勿重复获取");
  }
};
const timerFN = (time = 60) => {
  let timer = setInterval(() => {
    time -= 1;
    codeText.value = time;
    if (time <= 0) {
      clearInterval(timer);
      codeText.value = "获取验证码";
    }
  }, 1000);
};
const register = () => {
  ref_form.value
    ?.validate()
    .then((valid) => {
      if (valid) {
        console.log(valid);
        ref_toast.value?.showToast("注册成功");
      } else {
        ref_toast.value?.warning("请先完善表单！");
      }
    })
    .catch((err: any) => {
      // 处理验证错误
      console.log("校验失败", err);
      ref_toast.value?.warning("请先完善表单！");
      setTimeout(() => {
        ref_form.value?.clearValidate();
      }, 4000);
    });
  // ref_toast.value?.netFail()
  // ref_toast.value?.info('正在载入中..')
  // ref_toast.value?.warning('请先完善表单！')
  // ref_toast.value?.success('注册成功~')
  // ref_toast.value?.fail()
  // ref_toast.value?.info()
};
const toLogin = () => {
  // uni.navigateBack({
  // 	delta: 1
  // })
  uni.redirectTo({
    url: "/pages/login/login",
  });
};
const changeTelType = () => {
  console.log("执行");
};
const toRegister = () => {
  uni.navigateTo({
    url: "/pages/login/views/register",
  });
};

const returnSetp = () => {
  uni.navigateBack({
    delta: 1,
  });
};
</script>

<style scoped>
.pageBox {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}
.main {
  width: 100%;
  height: auto;
  padding: 40rpx 0rpx;
  /* border: 1px solid ; */
}
.inputBox {
  margin-top: 60rpx;
  position: relative;
  width: 80%;
  margin-left: 10%;
  border: 1px solid #ccc !important;
  border-radius: 10px !important;
}
.inputBox::before {
  content: attr(data-label);
  position: absolute;
  top: -25rpx;
  left: 40rpx;
  padding: 0rpx 20rpx;
  width: 90rpx;
  height: 40rpx;
  background-color: white !important;
  font-size: var(--size-3);
  color: var(--bk-2);
}
input {
  margin-top: 5rpx;
}
.submit {
  width: 86%;
  margin-left: 7%;
  margin-top: 60rpx;
  height: 82rpx;
  background: linear-gradient(80deg, #677bf0, #2330df);
  box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0, 0, 0, 0.2);
  border-radius: 0rpx 41rpx 41rpx 41rpx;
  color: white;
}
.fnBox1 {
  margin-top: 30rpx;
  width: 90%;
  /* border: 1px solid ; */
  text-align: right;
  font-size: 24rpx;
  line-height: 40rpx;
  color: gray;
  letter-spacing: 1rpx;
}
.verifyCode {
  transition: all 1s;
}
</style>
