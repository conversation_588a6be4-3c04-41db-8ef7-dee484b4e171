"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "head",
  props: {
    title: {
      type: String,
      default: "选择部门"
    },
    // 返回类型：'home'返回首页，'back'返回上一级
    returnType: {
      type: String,
      default: "home"
    },
    delta: {
      type: Number,
      default: 1
    }
  },
  setup(__props) {
    const props = __props;
    const onReturn = () => {
      if (props.returnType === "back") {
        common_vendor.index.navigateBack({
          delta: props.delta
        });
      } else if (props.returnType === "work") {
        common_vendor.index.switchTab({
          url: "/pages/work/work"
        });
      } else if (props.returnType === "mine") {
        common_vendor.index.switchTab({
          url: "/pages/mine/mine"
        });
      } else {
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
      }
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(onReturn),
        b: common_vendor.t(props.title)
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e012c461"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/components/head.vue"]]);
wx.createComponent(Component);
