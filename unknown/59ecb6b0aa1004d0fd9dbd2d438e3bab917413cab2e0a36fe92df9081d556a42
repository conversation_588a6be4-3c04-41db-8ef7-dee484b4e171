<view class="basic-evaluation data-v-40206502"><view wx:if="{{a}}" class="flex data-v-40206502" bindtap="{{b}}"><view class="shang data-v-40206502">返回上一级</view><view class="data-v-40206502" style="margin-right:5%;max-height:40rpx;padding:10rpx 0px"><image class="data-v-40206502" style="max-height:50rpx;max-width:40rpx;margin-right:5rpx" src="/static/index/back.png" mode="aspectFit"></image></view></view><scroll-view scroll-y class="scroll-box data-v-40206502"><user-list wx:if="{{d}}" class="data-v-40206502" bindselect="{{c}}" u-i="40206502-0" bind:__l="__l" u-p="{{d}}"/><view class="noData data-v-40206502" hidden="{{!e}}"><view class="noData-image data-v-40206502"><image class="data-v-40206502" src="/static/index/noData.png" mode="aspectFit"/></view><view class="noData-text data-v-40206502">无数据哦</view></view></scroll-view><toast class="r data-v-40206502" u-r="ref_toast" u-i="40206502-1" bind:__l="__l"></toast></view>