<view class="user-list data-v-67e069a4"><up-collapse wx:if="{{a}}" class="data-v-67e069a4" u-s="{{['d']}}" u-i="67e069a4-0" bind:__l="__l"><view wx:for="{{b}}" wx:for-item="item" wx:key="r" class="cu-chat data-v-67e069a4" data-selected="false" bindtap="{{item.s}}"><view wx:if="{{item.a}}" class="cu-item data-v-67e069a4"><view class="picBox data-v-67e069a4"><image class="pic data-v-67e069a4" src="{{item.b}}" alt=""/></view><view class="row data-v-67e069a4"><view class="{{['data-v-67e069a4', item.h && 'row-left']}}"><view class="nameBox data-v-67e069a4">{{item.c}} <view class="tagW data-v-67e069a4"><up-tag wx:if="{{item.d}}" class="data-v-67e069a4" u-i="{{item.e}}" bind:__l="__l" u-p="{{item.f}}"/></view></view><view class="department data-v-67e069a4">{{item.g}}</view></view><view wx:if="{{item.i}}" class="row-right data-v-67e069a4"><up-tag wx:if="{{item.j}}" class="data-v-67e069a4" catchclick="{{item.k}}" u-i="{{item.l}}" bind:__l="__l" u-p="{{item.m}}"/><up-tag wx:if="{{item.n}}" class="data-v-67e069a4" catchclick="{{item.o}}" u-i="{{item.p}}" bind:__l="__l" u-p="{{item.q}}"/></view></view></view></view></up-collapse><view class="noData data-v-67e069a4" hidden="{{!c}}"><view class=" data-v-67e069a4" style="width:90%;margin-left:5%"><image class="data-v-67e069a4" src="/static/index/noData.png" mode="aspectFit"></image></view><view class=" data-v-67e069a4" style="width:100%;text-align:center;margin-top:20rpx;color:dodgerblue"> 无数据哦 </view></view></view>