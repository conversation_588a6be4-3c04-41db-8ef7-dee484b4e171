<template>
  <view class="pageBox">
    <view class="head">
      <view class="return" @click="store.backAPage()">
        <image
          class="return-img"
          src="../../../static/icons/fanhui.png"
          mode="aspectFit"></image>
      </view>
      <view class="title">匿名评价</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useIndexStore } from "../../../store/index";
import { useUserStore } from "../../../store/user";
import { onShow } from "@dcloudio/uni-app";
import toast from "@/components/toast.vue";
import type { IToast } from "@/types";
import { getUserInfo, setUserEmail } from "@/utils/http";
import { cantBeBad } from "@/utils";
const store = useIndexStore();
const userStore = useUserStore();
const ref_toast = ref<IToast>();
console.log(ref_toast.value);
</script>

<style></style>
