import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni()],
	server: {
	  port: 5173, // 设置服务启动端口号
	  open: false, // 设置服务启动时是否自动打开浏览器
	  cors: true, // 允许跨域
	  // 设置代理，根据我们项目实际情况配置
	  proxy: {
		'/api': {
		  target: "https://360.hlktech.com/Api/V1",
		  changeOrigin: true,
		  rewrite: (path) => path.replace(/^\/api/, '')
		},
	  },
	},
  resolve: {
      //配置文件扩展名
		extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json"],
	},
});
