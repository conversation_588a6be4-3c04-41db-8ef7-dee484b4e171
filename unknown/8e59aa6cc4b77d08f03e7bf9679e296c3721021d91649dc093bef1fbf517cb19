/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.pageBox.data-v-bf6f1753 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}
.head.data-v-bf6f1753 {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: relative;
}
.return.data-v-bf6f1753 {
  position: absolute;
  left: 30rpx;
}
.return-img.data-v-bf6f1753 {
  width: 40rpx;
  height: 40rpx;
}
.title.data-v-bf6f1753 {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
}
.main.data-v-bf6f1753 {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}
.loading.data-v-bf6f1753 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}
.loading-text.data-v-bf6f1753 {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}
.empty-list.data-v-bf6f1753 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}
.list-container.data-v-bf6f1753 {
  padding-bottom: 30rpx;
}
.list-item.data-v-bf6f1753 {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.item-header.data-v-bf6f1753 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 0.0625rem solid #f0f0f0;
  flex-wrap: wrap;
}
.target-info.data-v-bf6f1753 {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  flex: 1;
  min-width: 0;
  margin-right: 20rpx;
}
.department.data-v-bf6f1753 {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}
.status-wrapper.data-v-bf6f1753 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 100rpx;
}
.item-content.data-v-bf6f1753 {
  font-size: 28rpx;
}
.info-row.data-v-bf6f1753 {
  display: flex;
  margin-bottom: 16rpx;
}
.label.data-v-bf6f1753 {
  color: #666;
  min-width: 160rpx;
}
.value.data-v-bf6f1753 {
  color: #333;
  flex: 1;
  word-break: break-all;
}
.remarks.data-v-bf6f1753 {
  width: 600px;
}