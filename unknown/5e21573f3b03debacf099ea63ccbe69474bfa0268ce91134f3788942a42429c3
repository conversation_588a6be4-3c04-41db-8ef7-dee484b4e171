/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.head.data-v-96f699ac {
  background: linear-gradient(100deg, #677BF0, #2330DF);
  box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0, 0, 0, 0.2);
  color: white;
}
.stepBox.data-v-96f699ac {
  width: 100%;
  height: 200rpx;
  background: linear-gradient(80deg, #677BF0, #2330DF);
  box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0, 0, 0, 0.2);
}
.num.data-v-96f699ac {
  margin-top: 10rpx !important;
  border: 2px solid #2330DF !important;
  padding: 5px;
  line-height: 30px !important;
  font-size: 1.1rem !important;
}
.cu-steps .cu-item[class*=text-] .num.data-v-96f699ac::after {
  background-color: #2330DF;
}
.uploadBox.data-v-96f699ac {
  margin: 60rpx auto;
  width: 90%;
  padding: 40rpx;
  box-shadow: 0 0 4px #ccc;
  display: flex;
  border-radius: 10px;
}
.uploadBox > view.data-v-96f699ac {
  width: 20%;
  height: 80rpx !important;
  height: -moz-fit-content;
  height: fit-content;
  flex: 1;
}
.IDCard-image.data-v-96f699ac {
  width: 100%;
  height: 100%;
}
.uploadBox_text.data-v-96f699ac {
  flex: 4 !important;
  text-indent: 20rpx;
  line-height: 42rpx;
  letter-spacing: 1rpx;
}
.right-image.data-v-96f699ac {
  margin-left: 50rpx;
  margin-top: 22rpx;
  width: 45rpx;
  height: 40rpx;
}
.checking.data-v-96f699ac {
  margin-top: 20rpx;
  width: 100%;
  height: auto;
  padding: 20rpx 0px;
  text-align: center;
}
.ball.data-v-96f699ac {
  width: 80%;
  max-height: 300rpx;
}
.checked.data-v-96f699ac {
  margin: 60rpx auto;
  width: 90%;
  padding: 40rpx;
  box-shadow: 0 0 4px #ccc;
  display: flex;
  border-radius: 10px;
}
.checked > view.data-v-96f699ac {
  width: 20%;
  height: 80rpx !important;
  height: -moz-fit-content;
  height: fit-content;
  flex: 1;
}
.IDCard-image.data-v-96f699ac {
  width: 100%;
  height: 100%;
}
.uploadBox_text.data-v-96f699ac {
  flex: 4 !important;
  text-indent: 20rpx;
  line-height: 42rpx;
  letter-spacing: 1rpx;
}
.right-image.data-v-96f699ac {
  margin-left: 50rpx;
  margin-top: 22rpx;
  width: 45rpx;
  height: 40rpx;
}