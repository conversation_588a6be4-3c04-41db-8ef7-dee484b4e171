<view class="login data-v-cdfe2409"><view class="header data-v-cdfe2409"><image class="login_head_bg data-v-cdfe2409" src="/static/mine/login_head.png" mode=""></image></view><view class="main data-v-cdfe2409"><up-form wx:if="{{i}}" class="r data-v-cdfe2409" u-s="{{['d']}}" u-r="ref_form" u-i="cdfe2409-0" bind:__l="__l" u-p="{{i}}"><up-form-item wx:if="{{d}}" class="data-v-cdfe2409" u-s="{{['d']}}" u-i="cdfe2409-1,cdfe2409-0" bind:__l="__l" u-p="{{d}}"><view class="cu-form-group inputBox data-v-cdfe2409" data-label="账号"><view class="title data-v-cdfe2409"><button class="cu-btn round sm data-v-cdfe2409" style="font-size:var(--size-2);letter-spacing:2rpx;color:var(--bk-3);background-color:transparent">{{a}}</button></view><up-input wx:if="{{c}}" class="data-v-cdfe2409" u-i="cdfe2409-2,cdfe2409-1" bind:__l="__l" bindupdateModelValue="{{b}}" u-p="{{c}}"/></view></up-form-item><up-form-item wx:if="{{g}}" class="data-v-cdfe2409" u-s="{{['d']}}" u-i="cdfe2409-3,cdfe2409-0" bind:__l="__l" u-p="{{g}}"><view class="cu-form-group inputBox data-v-cdfe2409" data-label="密码"><up-input wx:if="{{f}}" class="data-v-cdfe2409" u-i="cdfe2409-4,cdfe2409-3" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"/></view></up-form-item></up-form><view class="cu-form-group data-v-cdfe2409" style="border:none"><button class="submit data-v-cdfe2409" bindtap="{{j}}">登录</button></view><view class="fnBox1 data-v-cdfe2409" style="text-align:center;text-indent:90rpx"> 忘记密码？ <text class="data-v-cdfe2409" style="color:#5266e2" bindtap="{{k}}">点我申诉</text></view></view><toast class="r data-v-cdfe2409" u-r="ref_toast" u-i="cdfe2409-5" bind:__l="__l"></toast></view>