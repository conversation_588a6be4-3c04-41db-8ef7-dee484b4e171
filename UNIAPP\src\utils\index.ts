// ------------------------------------>  工具 / 辅助 函数  <------------------------------------

import { useIndexStore } from "@/store";
import CryptoJS from "crypto-js";
export const phoneRegex =
  /^(13[0-9]{9})|(18[0-9]{9})|(14[0-9]{9})|(17[0-9]{9})|(15[0-9]{9})$/; //手机号

/**
 * 去除 -对象中- 的空值 、 null 、 undefined --stayKeys 保留的空值
 *
 * @param {object} obj - 传入需要处理的对象.
 * @param {string[]} [stayKeys] - 需要保留的空值的key.
 */
export const deleteEmptyValueObj = <T>(obj: T, stayKeys?: string[]): T => {
  for (const key in obj) {
    if (obj[key] === null || obj[key] === undefined || obj[key] === "") {
      if (!stayKeys?.includes(key)) {
        delete obj[key];
      }
    }
  }
  return obj;
};

/** 计算流量单位 */
export const computeRate = (value: number) => {
  if (value < 1024) {
    return value.toFixed(2) + "B";
  } else if (value < 1024 * 1024) {
    return (value / 1024).toFixed(2) + "KB";
  } else if (value < 1024 * 1024 * 1024) {
    return (value / 1024 / 1024).toFixed(2) + "MB";
  } else if (value < 1024 * 1024 * 1024 * 1024) {
    return (value / 1024 / 1024 / 1024).toFixed(2) + "GB";
  } else {
    return 0;
  }
};
/** 合法值 -排除null、undefined、空字符串 */
export const identifyValue = (value: any) => {
  if (typeof value != "string") return false; //排除非法值
  return ![undefined, null, ""].includes(value);
};

/** 对象里的值为数字的转成number类型 */
export function convertNumberValues(obj: any) {
  for (const [key, value] of Object.entries(obj)) {
    if (!isNaN(value)) {
      obj[key] = Number(value);
    }
  }
  return obj;
}
/** 转译成中国时间 */
export function convertTimestampToUTC(timestamp: number) {
  const date = new Date(timestamp);
  const year = date.getUTCFullYear();
  const month = date.getUTCMonth() + 1;
  const day = date.getUTCDate();
  const hours = date.getUTCHours();
  const minutes = date.getUTCMinutes();
  const seconds = date.getUTCSeconds();

  const chineseMonths = [
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
    "11",
    "12",
  ];
  const chineseDays = ["日", "一", "二", "三", "四", "五", "六"];

  const chineseDate = `${year}年${
    chineseMonths[month - 1]
  }月${day}日 ${hours}时${minutes}分${seconds}秒`;

  return chineseDate;
}
/** utc 时间转译 */
export function timestampToUTCDate(timestamp: number) {
  const utcTime = new Date(timestamp * 1000);
  const year = utcTime.getUTCFullYear();
  const month = utcTime.getUTCMonth() + 1;
  const day = utcTime.getUTCDate();
  const hour = utcTime.getUTCHours();
  const minute = utcTime.getUTCMinutes();
  const second = utcTime.getUTCSeconds();
  const weekday = [
    "星期日",
    "星期一",
    "星期二",
    "星期三",
    "星期四",
    "星期五",
    "星期六",
  ][utcTime.getUTCDay()];
  const chineseDate = `${year}年${month}月${day}日 ${weekday} ${hour}:${minute}:${second}`;
  return chineseDate;
}
/* souece 原字符串 start 要截取的位置 newStr 要插入的字符 -在某处插入新字符 */
export function insertStr(source: string, start: number, newStr: string) {
  return source.slice(0, start) + newStr + source.slice(start);
}
import { getBaseUrl } from "@/utils/request";
/** 检测当前环境 */
export async function getCurrentEnvironment() {
  // @ts-ignore (忽略类型检查，因为 uni-app 的类型定义可能还未更新)
  const appBaseInfo = uni.getAppBaseInfo();
  const platform = appBaseInfo?.host?.env?.toLowerCase();
  // console.log("当前环境 -->", appBaseInfo.hostName);

  if (
    platform === "wechat" ||
    appBaseInfo.hostName?.toLowerCase() === "wechat"
  ) {
    const http = getBaseUrl();
    // return "https://360.hlktech.com/Api/V1";
    // return 'http://localhost:9225/Api/V1'
    return http;
  } else {
    return "/api";
  }
}
/** 普通提示 */
export const uniToast = (title: string = "网络请求失败") =>
  uni.showToast({
    icon: "none",
    duration: 3000,
    title,
  });
/** 字符串长度不能为0 undefined null */
export const cantBeBad = (value: string) => {
  const http = getBaseUrl().replace(/\Api\/V1$/, "");
  // console.log("返回", value, http);
  if (value != "" && value != undefined && value != null) {
    value = http + value;
    return value;
  } else {
    return "../../static/images/avatar.png";
  }
};
/** 获取当前日期 */
export function getCurrentDate() {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = String(currentDate.getMonth() + 1).padStart(2, "0");
  const day = String(currentDate.getDate()).padStart(2, "0");

  return `${year}/${month}/${day}`; // 输出类似于：2024/10/17
}
/** 转成中文数字 */
export function convertToChinaNumber(index: number) {
  const chineseNumbers = [
    "一",
    "二",
    "三",
    "四",
    "五",
    "六",
    "七",
    "八",
    "九",
    "十",
  ];
  if (index <= 9) {
    return chineseNumbers[index];
  } else {
    index = Number(String(index).slice(0, 1)) - 1;
    console.log("这里");
    return "十" + chineseNumbers[index];
  }
}
/** 计算MD5 hash  */
export function generateHash(str: string) {
  const hash = CryptoJS.MD5(str).toString().toUpperCase();
  // console.log(hash);
  return hash;
}
/** 获取某年至今的月份 */
export function getTheYearMonths(year?: number) {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;
  // 如果没传年份进来就默认等于今年
  if (!year) {
    year = currentYear;
  }

  if (year !== undefined && year !== currentYear) {
    return Array.from({ length: 12 }, (_, index) =>
      `${index + 1}`.length === 1
        ? `${year}/0${index + 1}`
        : `${year}/${index + 1}`
    );
  } else {
    return Array.from({ length: currentMonth }, (_, index) =>
      `${index + 1}`.length === 1
        ? `${year}/0${index + 1}`
        : `${year}/${index + 1}`
    );
  }
}
/** 通过时间戳找出月份 */
export function getYearsDistanceFrom2024() {
  const years = [];
  const currentYear = new Date().getFullYear();
  for (let year = 2025; year <= currentYear; year++) {
    years.push(year);
  }
  return years;
}
/** 隐藏掉手机中间几位*/
export function hidePhoneNumber(phoneNumber: string) {
  const hiddenPhoneNumber = phoneNumber.replace(
    /(\d{3})\d{5}(\d{2})/,
    "$1*****$2"
  );
  return hiddenPhoneNumber;
}
