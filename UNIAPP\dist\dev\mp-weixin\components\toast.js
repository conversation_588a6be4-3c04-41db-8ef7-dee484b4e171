"use strict";
const common_vendor = require("../common/vendor.js");
if (!Array) {
  const _easycom_up_toast2 = common_vendor.resolveComponent("up-toast");
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_modal2 = common_vendor.resolveComponent("up-modal");
  (_easycom_up_toast2 + _easycom_up_icon2 + _easycom_up_modal2)();
}
const _easycom_up_toast = () => "../node-modules/uview-plus/components/u-toast/u-toast.js";
const _easycom_up_icon = () => "../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_modal = () => "../node-modules/uview-plus/components/u-modal/u-modal.js";
if (!Math) {
  (_easycom_up_toast + _easycom_up_icon + _easycom_up_modal)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "toast",
  setup(__props, { expose: __expose }) {
    const uToastRef = common_vendor.ref(null);
    common_vendor.ref(null);
    const show = common_vendor.ref(false);
    let showModel = common_vendor.ref(false);
    let modelText = common_vendor.ref("朕知道了");
    let modelIcon = common_vendor.ref("none");
    let modelColor = common_vendor.ref("white");
    let modelStyle = common_vendor.ref("background-color: rgba(0, 0, 0, 0.5);color: white;");
    let defualtTime = common_vendor.ref(3500);
    let hideModal = () => {
      showModel.value = false;
    };
    const closeConfirm = () => {
      show.value = false;
      setTimeout(() => {
        common_vendor.index.switchTab({
          url: "../pages/index/index"
        });
      }, 2e3);
    };
    const info = (text = "执行成功", time = defualtTime.value) => {
      common_vendor.index.showToast({
        title: text,
        duration: time,
        icon: "none"
      });
    };
    const success = (text = "操作成功", time = defualtTime.value, icon = "checkbox-mark") => {
      showModel.value = false;
      showModel.value = true;
      modelText.value = text;
      modelIcon.value = icon;
      modelColor.value = "#39b54a";
      modelStyle.value = "background:linear-gradient(35deg,#c6f0ce , #90f1bc);color: #35b142;";
      time === 0 ? time = 99999 : "";
      setTimeout(() => {
        showModel.value = false;
      }, time);
    };
    const warning = (text = "操作告警", time = defualtTime.value, icon = "arrow-up-fill") => {
      showModel.value = false;
      showModel.value = true;
      modelText.value = text;
      modelIcon.value = icon;
      modelStyle.value = "background:linear-gradient(-45deg,#f1a265,#ffb472);color: white;letter-spacing: 4rpx;box-shadow:0 0 10px #666666;";
      modelColor.value = "white";
      time === 0 ? time = 99999 : "";
      setTimeout(() => {
        showModel.value = false;
      }, time);
    };
    const fail = (text = "操作失败", time = defualtTime.value, icon = "close") => {
      showModel.value = false;
      showModel.value = true;
      modelText.value = text;
      modelIcon.value = icon;
      modelStyle.value = `background: linear-gradient(160deg, #ff5967 60%, rgb(220, 140, 160));color: #FFFFFF;font-size:16px;box-shadow:0 0 10px #666666;`;
      modelColor.value = "white";
      time === 0 ? time = 99999 : "";
      setTimeout(() => {
        showModel.value = false;
      }, time);
    };
    const netFail = (text = "网络连接失败", time = defualtTime.value, icon = "wifi-off") => {
      showModel.value = false;
      showModel.value = true;
      modelText.value = text;
      modelIcon.value = icon;
      modelStyle.value = "background-color: rgba(0,0,0,0.6);color: white;";
      modelColor.value = "red";
      time === 0 ? time = 99999 : "";
      setTimeout(() => {
        showModel.value = false;
      }, time);
    };
    __expose({
      info,
      success,
      warning,
      fail,
      netFail
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.sr(uToastRef, "d820d238-0", {
          "k": "uToastRef"
        }),
        b: common_vendor.unref(modelIcon) != "none"
      }, common_vendor.unref(modelIcon) != "none" ? {
        c: common_vendor.p({
          name: common_vendor.unref(modelIcon),
          color: common_vendor.unref(modelColor),
          size: "5.7vw"
        })
      } : {}, {
        d: common_vendor.t(common_vendor.unref(modelText)),
        e: common_vendor.o(
          //@ts-ignore
          (...args) => common_vendor.unref(hideModal) && common_vendor.unref(hideModal)(...args)
        ),
        f: common_vendor.s(common_vendor.unref(modelStyle)),
        g: common_vendor.n(common_vendor.unref(showModel) ? "show" : ""),
        h: common_vendor.o(
          //@ts-ignore
          (...args) => common_vendor.unref(hideModal) && common_vendor.unref(hideModal)(...args)
        ),
        i: common_vendor.o(closeConfirm),
        j: common_vendor.p({
          show: show.value,
          title: "提交结果",
          confirmText: "好的"
        })
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/components/toast.vue"]]);
wx.createComponent(Component);
