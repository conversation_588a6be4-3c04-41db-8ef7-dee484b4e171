
.login.data-v-cdfe2409 {
  width: 100%;
  height: 100vh;
  background-color: white;
  /* background-color: skyblue; */
}
.header.data-v-cdfe2409 {
  width: 536rpx;
  height: 400rpx;
  /* 		background-image: url('../../static/mine/login_head.png');
		background-size: 100% 100%;
		background-repeat: no-repeat;
		position: relative; */
}
.login_head_bg.data-v-cdfe2409 {
  filter: drop-shadow(4px 3px 3px #ccc);
}
.main.data-v-cdfe2409 {
  width: 100%;
  height: auto;
  padding: 40rpx 0rpx;
  /* border: 1px solid ; */
}
.inputBox.data-v-cdfe2409 {
  margin-top: 40rpx;
  position: relative;
  width: 80%;
  margin-left: 10%;
  border: 1px solid #ccc !important;
  border-radius: 10px !important;
}
.inputBox.data-v-cdfe2409::before {
  content: attr(data-label);
  position: absolute;
  top: -25rpx;
  left: 40rpx;
  padding: 0rpx 20rpx;
  width: 90rpx;
  height: 40rpx;
  background-color: white !important;
  font-size: var(--size-3);
  color: var(--bk-2);
}
input.data-v-cdfe2409 {
  margin-top: 5rpx;
}
.submit.data-v-cdfe2409 {
  width: 55%;
  margin-left: 24%;
  margin-top: 30rpx;
  height: 82rpx;
  background: linear-gradient(80deg, #677bf0, #2330df);
  box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0, 0, 0, 0.2);
  border-radius: 0rpx 41rpx 41rpx 41rpx;
  color: white;
}
.fnBox1.data-v-cdfe2409 {
  margin-top: 20rpx;
  width: 90%;
  /* border: 1px solid ; */
  text-align: right;
  font-size: 24rpx;
  line-height: 40rpx;
  color: gray;
  letter-spacing: 1rpx;
}
.verifyCode.data-v-cdfe2409 {
  transition: all 1s;
}
