<template>
  <view class="pageBox">
    <view class="head">
      <view class="return" @click="back()">
        <image
          class="return-img"
          src="../../../static/icons/fanhui.png"
          mode="aspectFit"></image>
      </view>
      <text class="title">打分评价</text>
    </view>
    <view class="content1"></view>
    <view class="content2"></view>
    <view class="content">
      <view class="contentHead">
        <view style="display: flex; margin-bottom: 10rpx">
          {{
            userInfo.DisplayName != userStore.userInfo.DisplayName
              ? "被评者"
              : "姓名"
          }}：{{ userInfo.DisplayName }}
          <view
            @click="changeUser()"
            style="color: dodgerblue; margin-left: -15rpx"
            v-if="store.submitData.TId != 0">
            <up-icon
              name="person-delete-fill"
              size="3vh"
              color="crimson"
              style="
                margin-top: -9rpx;
                filter: drop-shadow(2px 0 1px #f7f7f7);
              "></up-icon>
          </view>
          <view style="margin-right: 30rpx"></view>
          <view class="overText" style="white-space: nowrap">
            职位：
            <text class="overText" style="white-space: nowrap">
              {{ userInfo.Ex4 || "无" }}
            </text>
          </view>
          <!-- -->
          <!-- <text style="color: #666666;">性别：</text>
					<view style="margin-left: -30rpx;margin-top: 7rpx;" v-if="userInfo.Sex != 0">
						<up-icon name="man" size="4vw"
						:color="'dodgerblue'" v-if="userInfo.Sex === 1"></up-icon>
						<up-icon name="woman" size="4vw"
						:color="'#ff557f'" :bold="true" v-if="userInfo.Sex === 2"></up-icon>
					</view>
					<view v-else style="margin-left: -30rpx;">
						保密
					</view> -->
        </view>
        <view style="line-height: 42rpx">
          <text class="text-grey">{{ getCurrentDate() }}</text>
        </view>
        <image
          class="tx"
          style="border-radius: 50%; border: 1px solid #f1f1f1"
          :src="
            cantBeBad(userInfo.Avatar) || '../../../static/images/avatar.png'
          "
          mode="aspectFill"></image>
      </view>
      <view class="contentTitle">
        <view>
          {{ orginalList[questionRenderIndex]?.Name }}
        </view>
      </view>
      <view class="questionContainer">
        <view
          class=""
          style="margin-top: 20vh"
          v-if="questionList.length === 0">
          <up-loading-icon size="3vh"></up-loading-icon>
          <view
            style="
              text-align: center;
              font-size: 24rpx;
              color: #ccc;
              margin-top: 15rpx;
              letter-spacing: 1rpx;
            ">
            题库加载中...
          </view>
        </view>
        <!-- <view v-else class="questionBox" v-for="item,index in questionList" :key="item">
					<view class="questionTitle">
					{{ convertToChinaNumber(index) }}.	{{item.Name}}
					</view> -->
        <view
          class="questionContent"
          v-for="(item1, index2) in questionList"
          :key="item1.Id">
          <view class="questionText">
            {{ index2 + 1 }}. {{ item1.Content }}
          </view>
          <view class="questionStar">
            <up-rate
              :count="5"
              allowHalf
              size="3.2vh"
              inactiveColor="#a6bcc8"
              v-model="item1.value"></up-rate>
            <text style="margin-left: 20rpx">{{ item1.value }} 分</text>
          </view>
        </view>
        <!-- </view> -->
      </view>
      <view class="tips">
        <view>请各部门、各同事单独填写此评价表，不要和任何人进行讨论</view>
        <view>本次评估由公司汇总保密，不会像被评估人反馈，请各位如实填写</view>
      </view>
    </view>
    <view class="bottom">
      <!-- -->
      <text style="letter-spacing: 1rpx">
        第 {{ questionRenderIndex + 1 }} 大题 共 {{ orginalList?.length }} 小题
      </text>
      <view
        @click="backQuestion()"
        style="margin-left: auto"
        v-show="questionRenderIndex > 0">
        上一题
      </view>
      <view @click="nextPage()" style="margin-left: auto">下一题</view>
    </view>
    <toast ref="ref_toast"></toast>
  </view>
</template>

<script setup lang="ts">
import { useIndexStore } from "@/store";
import { getCurrentDate } from "@/utils";
import toast from "../../../components/toast.vue";
import { onMounted, ref, watch } from "vue";
import type { IQuestion, IToast, QuestionData, QuestionData2 } from "@/types";
import { getQuestions } from "@/utils/http";
import { useUserStore } from "@/store/user";
import { cantBeBad } from "@/utils";
import { onShow } from "@dcloudio/uni-app";
const ref_toast = ref<IToast>();
const store = useIndexStore();
const userStore = useUserStore();
const userInfo = ref(store.routerUserinfo);
const tiBig = ref(0);
const tiSmall = ref(0);
if (store.submitData.TId == 0) {
  //当前登录用户的个人信息
  userInfo.value = userStore.userInfo;
} else {
  //选择部门后的人信息
  userInfo.value = store.routerUserinfo;
  //   console.log(" id --->", store.submitData.BId, userInfo.value?.ID);
  store.submitData.BId = userInfo.value?.ID;
}
if (userInfo.value.Avatar?.length === 0 || !userInfo.value.Avatar) {
  //默认头像
  userInfo.value.Avatar = "/static/images/avatar.png";
}
const questionRenderIndex = ref(0);
const orginalList = ref<IQuestion>([]);
const questionList = ref<QuestionData[]>([]);
const totalValueList = ref<number[]>([]);
const contentList = ref<QuestionData2[]>([]); //
const currentScore = ref(0);
/** 当选择星星时 */
// const onSelectStar = function (e) {
// 	console.log(e);
// }
/** 一键填满 */
const full = async (isFull: boolean = false): Promise<boolean> => {
  // 不要满分处理
  return await new Promise((r) => {
    if (
      questionRenderIndex.value >= 8 &&
      questionRenderIndex.value <= 10 &&
      isFull === false
    ) {
      questionList.value.map((item, _index) => {
        if (Number((Math.random() * 10).toFixed(0)) >= 5 && _index % 2 === 0) {
          item.value = 5;
        } else {
          item.value = 4;
        }
      });
      return r(true);
    }
    questionList.value.map((item) => {
      item.value = 5;
    });
    r(true);
  });
};
// 点击返回时弹出是否确认返回
const back = () => {
  if (questionRenderIndex.value > 0) {
    uni.showModal({
      title: "警告",
      content: "你有未完成表单是否继续退出？",
      success: (res) => {
        if (res.confirm) {
          uni.navigateBack({
            delta: 1,
          });
        }
      },
    });
  } else {
    uni.navigateBack({
      delta: 1,
    });
  }
};
const fullAll = async () => {
  // const len = orginalList.value.length
  if (questionRenderIndex.value === 10) {
    return true;
  }
  await full(false);
  await nextPage();
  setTimeout(async () => {
    await fullAll();
  }, 300);
};
/** 重新选择用户 */
const changeUser = function () {
  uni.navigateBack({
    delta: 1,
  });
};
/** 计算单个答题总分 */
const computeValue = (list: number[], fractions: number[] = []) => {
  // 获取当前大题的权重
  const currentWeights = orginalList.value[questionRenderIndex.value].Weights;
  // 如果没有提供小题占比，则使用平均分配
  if (fractions.length === 0 || fractions.length !== list.length) {
    const sum = list.reduce((a, b) => a + b, 0);
    //  console.log("sum (平均分配)", sum);

    const data = (sum / (list.length * 5)) * currentWeights;
    //  console.log("data (平均分配)", data);
    return data.toFixed(2);
  }

  // 使用小题占比计算加权总分
  let weightedSum = 0;
  for (let i = 0; i < list.length; i++) {
    // 计算每个小题的满分值 = 总分 * 大题占比 * 小题占比
    const maxScore = 100 * (currentWeights / 100) * (fractions[i] / 100);
    // 单星价值 = 小题满分 / 5
    const starValue = maxScore / 5;
    // 小题得分 = 单星价值 * 用户选择的星数
    const score = starValue * list[i];

    //  console.log(
    //    `小题${i + 1}: 满分=${maxScore.toFixed(2)}, 单星价值=${starValue.toFixed(
    //      2
    //    )}, 选择${list[i]}星, 得分=${score.toFixed(2)}`
    //  );

    weightedSum += score;
  }
  //   console.log("weightedSum (加权总分)", weightedSum.toFixed(2));

  return weightedSum.toFixed(2);
};
/** 上一题 */
const backQuestion = async () => {
  // 先把下标 -= 1
  questionRenderIndex.value -= 1;
  // console.log('完成',contentList.value,totalValueList.value);
  totalValueList.value.splice(questionRenderIndex.value, 1);
  questionList.value = orginalList.value[questionRenderIndex.value].List; //题目还原
  const backCount = orginalList.value[questionRenderIndex.value].List.length; //上一题的题目长度
  const len = contentList.value.length;
  // 把评分还原
  questionList.value.map((item, index) => {
    console.log(contentList.value[len - backCount].Value);
    item.value = Number(contentList.value[len - (backCount - index)].Value);
  });
  contentList.value.splice(len - backCount, backCount);
  return;
};
/** 清除当前页面缓存数据 */
const clearData = () => {
  store.resetData(); //重置表单数据
  questionRenderIndex.value = 0; //重置大题下标
  totalValueList.value = []; //清除总数
  contentList.value = []; //清除content
};
/** 点击下一题 */
const nextPage = async function () {
  // 需求：
  // 1.当前题目的总分
  // 2.当前题目的打分和id
  // 3.所有题目总分的和
  if (questionRenderIndex.value > orginalList.value.length) {
    clearData();
    return;
  }
  //  首先 看看有没有没填的题
  if (questionList.value?.find((item) => item.value === 0)) {
    ref_toast.value?.warning("您有未完成的题目");
    return;
  }
  // 01.算出5题的总分
  const list = questionList.value.map((item) => item.value);
  //   console.log("list", list);

  // 计算小题占比 Fraction
  // 如果小题没有占比，则根据大题权重平均分配
  const currentWeights = orginalList.value[questionRenderIndex.value].Weights;
  let hasFractions = questionList.value.every(
    (item) => item.Fraction !== undefined && item.Fraction > 0
  );

  if (!hasFractions) {
    // 平均分配占比（百分比形式）
    const averageFraction = 100 / questionList.value.length;
    questionList.value.forEach((item) => {
      item.Fraction = averageFraction;
    });
    // console.log('已平均分配小题占比，每题占比:', averageFraction, '%');
  } else {
    // 验证所有小题占比之和是否等于100%
    const totalFraction = questionList.value.reduce(
      (sum, item) => sum + (item.Fraction || 0),
      0
    );
    // console.log('小题占比总和:', totalFraction, '%');

    // 如果总和不等于100%，进行调整
    if (Math.abs(totalFraction - 100) > 0.001) {
      // 按比例调整
      const ratio = 100 / totalFraction;
      questionList.value.forEach((item) => {
        if (item.Fraction) {
          item.Fraction = Number((item.Fraction * ratio).toFixed(2));
        }
      });
      // console.log('已调整小题占比，调整比例:', ratio);
    }
  }

  // 获取所有小题的占比
  const fractions = questionList.value.map((item) => item.Fraction || 0);
  // console.log('fractions', fractions);

  // 使用加权计算总分
  totalValueList.value[questionRenderIndex.value] = Number(
    computeValue(list, fractions)
  );
  // 02.当前大题的所有题目打分和id
  questionList.value.forEach((item) =>
    contentList.value.push({ Id: item.Id, Value: String(item.value) })
  );
  //   console.log(
  //     `当前题（第${questionRenderIndex.value}题）`,
  //     totalValueList.value[questionRenderIndex.value]
  //   );
  // 最重要！！！
  // console.log('contentList.value', contentList.value);

  questionRenderIndex.value += 1;
  currentScore.value = totalValueList.value.reduce((a, b) => a + b, 0);
  // 04. 然后 看看有没有该完成了
  if (questionRenderIndex.value === orginalList.value.length) {
    //03.所有题目总分的和
    const sum = totalValueList.value.reduce((a, b) => a + b, 0);
    store.submitData.Score = `${sum.toFixed(2)}`; //综合分
    store.submitData.Content = contentList.value; //每道小题
    // console.log('结束->', store.submitData);
    uni.navigateTo({
      url: "./submit?tiBig=" + tiBig.value + "&tiSmall=" + tiSmall.value,
    });
    //questionRenderIndex.value -= 1
    return;
  }
  // 05. 如果还没到位 还能下一题的话...
  if (questionRenderIndex.value < orginalList.value.length) {
    return await getQuestionsAPI();
  }
};
/** 获取题目接口 */
const getQuestionsAPI = async function () {
  const Id = store.routerUserinfo.DepartmentId || 0;
  const res = await getQuestions({
    DepartmentId: Id,
    TargetUserId: store.submitData.BId,
  });
  console.log("题目", res);

  if (res.data.Code === 401 || res.data.Code === 403) {
    ref_toast.value?.info(res.data.Message);
    setTimeout(() => {
      uni.redirectTo({
        url: "../../login/login",
      });
    }, 3000);
    return;
  }
  const data: IQuestion = res.data.Data.listjson;
  tiBig.value = data.length;
  tiSmall.value = data.reduce((total, item) => {
    return total + (item.List?.length || 0);
  }, 0);
  // console.log("tiSmall :>> ", tiBig.value, tiSmall.value);
  // const list = []
  data.sort((a, b) => a.Id - b.Id);
  data.map((item) => {
    if (item.List.length > 0) {
      // 计算每个小题的平均占比（百分比形式）
      const averageFraction = 100 / item.List.length;

      item.List.map((item2) => {
        item2.value = 0;
        // 初始化小题占比，如果API没有返回Fraction，则平均分配
        if (!item2.Fraction) {
          item2.Fraction = averageFraction;
        }
      });

      // 验证所有小题占比之和是否等于100%
      const totalFraction = item.List.reduce(
        (sum, item2) => sum + (item2.Fraction || 0),
        0
      );

      // 如果总和不等于100%，进行调整
      if (Math.abs(totalFraction - 100) > 0.001) {
        // 按比例调整
        const ratio = 100 / totalFraction;
        item.List.forEach((item2) => {
          if (item2.Fraction) {
            item2.Fraction = Number((item2.Fraction * ratio).toFixed(2));
          }
        });
      }
    }
  });
  orginalList.value = data;
  if (!data[questionRenderIndex.value]) {
    setTimeout(() => ref_toast.value?.fail("题库丢失"), 2000);
    return;
  }
  questionList.value = data[questionRenderIndex.value].List;
};

getQuestionsAPI();
onMounted(() => {
  if (document) {
    document.addEventListener("keydown", function (event) {
      // 获取按下的键的键码
      if (event.key === "c" || event.key === "C") {
        full();
      }
    });
  }
  onShow(() => {
    if (uni.getStorageSync("jianyi") || false) {
      questionRenderIndex.value -= 1;
      uni.removeStorageSync("jianyi");
      return;
    }
  });
});
watch(totalValueList.value, (n) => {
  // console.log('totalValueList', n);
});
watch(questionList.value, (n) => {
  console.log("questionList.value", n);
});
</script>

<style scoped>
.pageBox {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.content {
  /* position: relative; */
  margin: 5rpx auto 0 auto;
  width: 92%;
  height: 75%;
  /* padding: 0rpx 0px; */
  border-radius: 25rpx;
  z-index: 3;
  /* overflow: hidden; */
  background-color: white;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(4, 0, 0, 0.12);
  /* border: 2px solid green; */
  /* border: 2px solid ; */
}

.content1 {
  position: absolute;
  top: 12.3%;
  left: 5%;
  width: 90%;
  height: 75%;
  /* background: red; */
  background-color: white;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(4, 0, 0, 0.1);
  border-radius: 25rpx;
  z-index: 2;
}

.content2 {
  position: absolute;
  top: 11.7%;
  left: 6%;
  width: 88%;
  height: 75%;
  /* background: green; */
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(4, 0, 0, 0.08);
  border-radius: 25rpx;
  background-color: white;
  z-index: 1;
}

.contentHead {
  display: flex;
  /* justify-content: space-around; */
  flex-direction: column;
  justify-content: right;
  font-size: 30rpx;
  padding: 20rpx 10rpx 0rpx 10rpx;
  /* border: 2px solid ; */
  text-indent: 10px;
}

.contentHead > view:nth-child(n + 2) {
  padding: 5rpx 0rpx;
}

.contentTitle {
  width: 100%;
  text-indent: 30rpx;
  font-size: 34rpx;
  line-height: 70rpx;
  height: 80rpx;
  letter-spacing: 1rpx;
  position: relative;
  /* border: 2px solid ; */
}

.contentTitle > image {
  margin-left: auto;
  margin-right: 40rpx;
  height: 120rpx;
  width: 120rpx;
  border-radius: 10px;
  /* border: 2px solid ; */
}

.contentTitle::before {
  position: absolute;
  content: "";
  left: 5%;
  bottom: 5%;
  width: 60rpx;
  height: 10rpx;
  background: linear-gradient(to right, dodgerblue 5%, blue);
  /* color: dodgerblue; */
  box-shadow: 0 0 2px #ccc;
  border-radius: 10px;
}

.questionContainer {
  margin-top: 30rpx;
  width: 92%;
  margin-left: 4%;
  height: 66%;
  padding: 10rpx 15rpx;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 3;
  /* border: 2px solid red; */
}

.questionBox {
  padding: 20rpx;
  /* border: 2px solid red; */
  font-size: 26rpx;
}

.questionContent {
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
}

.questionTitle {
  padding: 20rpx 0px;
  letter-spacing: 2rpx;
  font-size: 34rpx;
}

.questionText {
  color: #393939;
  width: 100%;
  font-size: 28rpx;
  letter-spacing: 1rpx;
  /* border: 2px solid red; */
}

.questionStar {
  margin-top: 10rpx;
  /* border: 2px solid red; */
  display: flex;
  place-items: center;
}

.tips {
  margin-top: 30rpx;
  width: 100%;
  /* margin-left: 5%; */
  padding: 10rpx;
  border-radius: 10px;
  z-index: 3;
  color: #666666;
  white-space: nowrap;
  /* border: 1px solid ; */
}

.tips > view:nth-child(1) {
  font-size: 22rpx;
  text-align: center;
}

.tips > view:nth-child(2) {
  margin-top: 10rpx;
  font-size: 23rpx;
  text-align: center;
}

.xavier {
  margin-top: auto;
  margin-bottom: 10rpx;
  color: transparent;
}

.xavier2 {
  position: fixed;
  bottom: 9%;
  color: #888888;
}

.bottom {
  padding: 40rpx;
  display: flex;
  width: 100%;
  border-top: 1px solid rgba(240, 240, 240, 1);
  color: #666666;
}

.tx {
  position: absolute;
  margin-top: 40rpx;
  right: 75rpx;
  width: 20vw;
  height: 20vw;
  box-shadow: 1px 0 4px rgba(240, 240, 240, 1);
  /* border: 2px solid #ccc; */
}
</style>
<style>
.uicon-star-fill > span {
  margin-left: 50px;
  width: 100px;
  height: 100px;
  padding: 10px;
  background-color: blue;
}

.u-rate__content__item__icon-wrap,
.u-rate__content__item__icon-wrap--half {
  min-width: 50% !important;
}
</style>
