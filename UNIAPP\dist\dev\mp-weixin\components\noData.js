"use strict";
const common_vendor = require("../common/vendor.js");
const common_assets = require("../common/assets.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "noData",
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  setup(__props) {
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !__props.data.length
      }, !__props.data.length ? {
        b: common_assets._imports_0
      } : {});
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4dce98b4"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/components/noData.vue"]]);
wx.createComponent(Component);
