"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_http = require("../../../utils/http.js");
require("../../../utils/request.js");
require("../../../utils/index.js");
require("../../../store/index.js");
require("../../../store/user.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
if (!Math) {
  (Head + MyTable)();
}
const Head = () => "../../../components/head.js";
const MyTable = () => "../../../components/myTable.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "statistics",
  setup(__props) {
    const title = common_vendor.ref("统计分");
    const formData = common_vendor.ref({});
    common_vendor.onLoad((e) => {
      formData.value = e;
      title.value = e.title;
      delete formData.value.title;
    });
    const columns = [
      {
        title: "自评总分",
        prop: "ZPScore",
        width: 1
      },
      {
        title: "互评平均分",
        prop: "HPScorepjf",
        width: 1
      },
      {
        title: "上级领导对下属评分",
        prop: "SPScore",
        width: 2
      },
      {
        title: "下评平均分",
        prop: "XPScorepjf",
        width: 1
      },
      {
        title: "综合总分",
        prop: "ZHScore",
        width: 1
      }
    ];
    const tableData = common_vendor.ref([
      // { id: 1, name: '张三', age: 28, job: '工程师
    ]);
    const getASStics = async () => {
      const res = await utils_http.getAppraisalStatistics(formData.value);
      const data = res.data.Data.listjson;
      common_vendor.index.hideLoading();
      tableData.value = [{ ...data }];
      console.log("表格数据:", tableData.value);
    };
    common_vendor.onShow(async () => {
      getASStics();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          title: title.value,
          returnType: "back"
        }),
        b: common_vendor.p({
          columns,
          data: tableData.value
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1c4c8a85"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pageA/pages/statistics/statistics.vue"]]);
wx.createPage(MiniProgramPage);
