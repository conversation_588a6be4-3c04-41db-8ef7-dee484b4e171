<template>
  <view class="pageBox">
    <Head title="申请列表" returnType="work"></Head>
    <view class="main delHead">
      <view class="loading" v-if="loading">
        <up-loading-icon size="3vh"></up-loading-icon>
        <view class="loading-text">数据加载中...</view>
      </view>
      <view class="empty-list" v-else-if="applicationList.length === 0">
        <view class="" style="width: 90%; margin-left: 5%">
          <image
            src="../../../static/index/noData.png"
            mode="aspectFit"></image>
        </view>
        <view
          class=""
          style="
            width: 100%;
            text-align: center;
            margin-top: 20rpx;
            color: dodgerblue;
          ">
          暂无申请记录
        </view>
      </view>
      <view class="list-container" v-else>
        <view
          class="list-item"
          v-for="item in applicationList"
          :key="item.Id"
          @click="onApprovalPending(item)">
          <view class="item-header">
            <view class="target-info">
              <text class="label">评测人：</text>
              <text class="value">{{ item.ApplicantName }}</text>
              <text class="department">({{ item.TargetDepartmentName }})</text>
            </view>
            <view class="status-wrapper">
              <up-tag
                :text="getStatusText(item.Status)"
                :type="getStatusType(item.Status)"
                size="mini"
                :plain="true" />
            </view>
          </view>
          <view class="item-content">
            <view class="info-row">
              <text class="label">被评人：</text>
              <text class="value">{{ item.TargetUserName }}</text>
            </view>
            <view class="info-row">
              <text class="label">申请时间：</text>
              <text class="value">{{ item.ApplyTimeStr }}</text>
            </view>
            <view class="info-row" v-if="item.Status !== 0">
              <text class="label">审批时间：</text>
              <text class="value">
                {{
                  item.ApproveTimeStr !== "0001-01-01 00:00"
                    ? item.ApproveTimeStr
                    : "未审批"
                }}
              </text>
            </view>
            <view class="info-row" v-if="item.ApproverName">
              <text class="label">审批人：</text>
              <text class="value">{{ item.ApproverName }}</text>
            </view>
            <view class="info-row">
              <text class="label">申请理由：</text>
              <text class="value">{{ item.Reason }}</text>
            </view>
            <view class="info-row" v-if="item.Remark">
              <text class="label">备注：</text>
              <text class="value">{{ item.Remark }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <toast ref="ref_toast"></toast>
  </view>
  <up-modal
    :show="showModal"
    title="审批备注"
    :closeOnClickOverlay="true"
    showConfirmButton="true"
    :show-cancel-button="true"
    confirmText="同意"
    cancelText="拒绝"
    @confirm="onConfirm"
    @cancel="onCancel"
    @close="onClose">
    <view class="slot-content remarks">
      <up-textarea
        cursor-spacing="100"
        v-model="state.formData.Remark"
        placeholder="请输入审批备注" />
    </view>
  </up-modal>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import {
  getSupervisorList,
  getApplyforList,
  getApplyforListRequest,
} from "@/utils/http";
import { onShow } from "@dcloudio/uni-app";
import { useUserStore } from "@/store/user";
import toast from "@/components/toast.vue";
import type { ACDRequest } from "@/types/http";
import Head from "@/components/head.vue";
// 定义响应式数据
const ref_toast = ref();
const userStore = useUserStore();
const loading = ref(true);
const applicationList = ref<ApplicationItem[]>([]);
const showModal = ref(false);

interface ApplicationItem {
  Id: string | number;
  ApplicantName: string;
  TargetDepartmentName: string;
  TargetUserName: string;
  ApplyTimeStr: string;
  ApproveTimeStr: string;
  ApproverName?: string;
  Status: number;
  Reason: string;
  Remark?: string;
}
//主管
const admin = userStore.IsDepartmentlanager;
const state = reactive({
  formData: {
    RequestId: "",
    Status: 0,
    Remark: "",
    Lng: "",
  },
});
const onConfirm = () => {
  showModal.value = false;
  state.formData.Status = 1;
  onSubmit();
};
const onCancel = () => {
  showModal.value = false;
  state.formData.Status = 2;
  onSubmit();
};
const onSubmit = async () => {
  if (!state.formData.Remark) {
    ref_toast.value?.warning("请输入审批备注");
    return;
  }
  const res = await getApplyforListRequest(state.formData as ACDRequest);
  uni.hideLoading();
  if (res.data.Code != 1) {
    ref_toast.value?.info(res.data.Message);
  } else {
    ref_toast.value?.success(res.data.Message);
    onReset();
  }
};
const onReset = () => {
  state.formData = {
    RequestId: "",
    Status: 0,
    Remark: "",
    Lng: "",
  };
  fetchApplicationList();
};
const onClose = () => {
  showModal.value = false;
};
const onApprovalPending = (e: any) => {
  if (e.Status == 1 || !admin) return;
  showModal.value = true;
  state.formData.RequestId = e.Id;
};
// 获取申请列表数据
const fetchApplicationList = async () => {
  loading.value = true;
  try {
    let res;
    if (admin) {
      // 主管获取审批列表
      res = await getSupervisorList();
    } else {
      // 普通用户获取自己的申请列表
      res = await getApplyforList();
    }
    uni.hideLoading();
    if (res && res.data && res.data.Data && res.data.Data.listjson) {
      applicationList.value = res.data.Data.listjson;
    } else {
      applicationList.value = [];
    }
    uni.hideLoading();
  } catch (error) {
    console.error("获取申请列表失败：", error);
    applicationList.value = [];
  } finally {
    loading.value = false;
  }
};

// 获取状态文本
// 0: 待审批
// 1: 已同意
// 2: 已拒绝
const getStatusText = (status: number) => {
  switch (status) {
    case 0:
      return "待审批";
    case 1:
      return "已通过";
    case 2:
      return "已拒绝";
    default:
      return "未知状态";
  }
};

// 获取状态类型
const getStatusType = (status: number) => {
  switch (status) {
    case 0:
      return "info";
    case 1:
      return "success";
    case 2:
      return "error";
    default:
      return "info";
  }
};

onShow(async () => {
  setTimeout(async () => {
    await fetchApplicationList();
  }, 300);
});

// 返回按钮处理函数
const onReturn = () => {
  uni.switchTab({
    url: "/pages/work/work",
  });
};
</script>

<style lang="scss" scoped>
.pageBox {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.head {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: relative;
}

.return {
  position: absolute;
  left: 30rpx;
}

.return-img {
  width: 40rpx;
  height: 40rpx;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
}

.main {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.empty-list {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.list-container {
  padding-bottom: 30rpx;
}

.list-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 0.0625rem solid #f0f0f0;
  flex-wrap: wrap;
}

.target-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  flex: 1;
  min-width: 0;
  margin-right: 20rpx;
}

.department {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}

.status-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 100rpx;
}

.item-content {
  font-size: 28rpx;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
}

.label {
  color: #666;
  min-width: 160rpx;
}

.value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

.remarks {
  width: 600px;
}
</style>
