"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_http = require("../../../utils/http.js");
require("../../../utils/request.js");
require("../../../utils/index.js");
require("../../../store/index.js");
require("../../../store/user.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  _easycom_up_icon2();
}
const _easycom_up_icon = () => "../../../node-modules/uview-plus/components/u-icon/u-icon.js";
if (!Math) {
  (Head + _easycom_up_icon + myTable + toast)();
}
const Head = () => "../../../components/head.js";
const toast = () => "../../../components/toast.js";
const myTable = () => "../../../components/myTable.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "hr",
  setup(__props) {
    const ref_toast = common_vendor.ref();
    const hrList = common_vendor.ref([]);
    const columns = common_vendor.ref([
      { title: "部门", prop: "Department", width: 1 },
      { title: "职位", prop: "Position", width: 1 },
      { title: "总分", prop: "TotalWeights", width: 1 }
    ]);
    const tableData = common_vendor.ref([]);
    common_vendor.onLoad(() => {
    });
    common_vendor.onShow(() => {
      getCRationality();
    });
    const getCRationality = async () => {
      var _a;
      const res = await utils_http.getCheckRationality();
      const data = res.data.Data.listjson;
      const code = res.data.Code;
      common_vendor.index.hideLoading();
      if (code != 1) {
        (_a = ref_toast.value) == null ? void 0 : _a.success("请求失败~");
      } else {
        if (data.length) {
          hrList.value = data;
          tableData.value = data;
        }
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "人事专用",
          returnType: "mine"
        }),
        b: tableData.value.length
      }, tableData.value.length ? {
        c: common_vendor.p({
          name: "error",
          color: "#ff5722"
        })
      } : {}, {
        d: common_vendor.p({
          columns: columns.value,
          data: tableData.value
        }),
        e: common_vendor.sr(ref_toast, "94b70ff6-3", {
          "k": "ref_toast"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-94b70ff6"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pageA/pages/hr/hr.vue"]]);
wx.createPage(MiniProgramPage);
