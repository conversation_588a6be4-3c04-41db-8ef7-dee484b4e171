
.header.data-v-d41d38da {
  width: 100%;
  height: 536rpx;
  border: 1px solid transparent;
  /* background-image: url('../../static/mine/head_bg.png');
		background-size: 100% 100%;
		background-repeat: no-repeat;
		background-position: -90rpx -10rpx; */
  position: relative;
}
.head_bg.data-v-d41d38da {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  /* border: 1px solid ; */
}
.header > view.data-v-d41d38da {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 50rpx;
  letter-spacing: 2rpx;
  text-align: center;
  display: flex;
  place-content: center;
  /* border: 1px solid ; */
}
.edit.data-v-d41d38da {
  position: absolute;
  top: 50%;
  width: 92% !important;
  text-align: left !important;
  text-indent: 20px;
  /* border: 1px solid red; */
}
.avatarBox.data-v-d41d38da {
  margin-top: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100% !important;
  height: 185rpx !important;
}
.avatar.data-v-d41d38da {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #ccc;
  box-shadow: 0 0 4px #8b95ed;
  border: 1px solid skyblue;
}
.avatar > image.data-v-d41d38da {
  width: 100%;
  height: 100%;
}
.main.data-v-d41d38da {
  width: 100%;
  height: 800rpx;
  /* border: 1px solid ; */
}
.main > .item.data-v-d41d38da {
  width: 90%;
  height: 80rpx;
  padding: 10rpx;
  margin: 25rpx auto;
  /* border: 1px solid gray; */
  display: flex;
  color: var(--bk-3);
}
.item > view.data-v-d41d38da {
  width: 100%;
  height: 100%;
  line-height: 60rpx;
  font-size: var(--size-4);
  letter-spacing: 2rpx;
  display: flex;
  justify-content: center;
  place-items: center;
  /* border: 1px solid #ccc; */
}
.item > view > image.data-v-d41d38da {
  width: 38rpx;
  height: 44rpx;
  /* border: 1px solid gray; */
}
