import { request } from "./request";
import type {
  Login,
  Assign,
  ACDRequest,
  QueryType,
  UpdateDeadline,
} from "../types/http";
import type { SetAppraisal } from "@/types";
import { generateHash } from "@/utils";
/** 登录接口 */
export const login = async (data: Login) => {
  const parm = {
    mobile: data.mobile,
    password: generateHash(data.password), // 确保密码加密后再发送
  };
  return await request({ url: "/Http2/Login", data: parm });
};
/** 获取openid */
export const getOpenidAPI = async (data: { code: string }) => {
  return await request({ url: "/WxOpen/GetOpenId", data });
};
/** 刷新token */
export const refreshToken = async (data: { OpenId: string }) => {
  return await request({ url: "/Http2/LoginByOpenId", data });
};
/** 非主管不显示上对下IsDepartmentManager,
 * 主管不显示互评IsAirDropManager */
export const getIsDepartmentlanager = async () => {
  return await request({ url: "/Http2/IsDepartmentManager" });
};
/* 申请跨部门互评;*/
export const getTransDepartment = async (data: Assign) => {
  return await request({ url: "/Http2/ApplyCrossDepartmentEvaluation", data });
};
/* 主管指定其他部门人员评价下属;*/
export const getAssign = async (data: Assign) => {
  return await request({ url: "/Http2/AssignCrossDepartmentEvaluator", data });
};
/* 获取审批的跨部门互评申请列表(主管);*/
export const getSupervisorList = async () => {
  return await request({
    url: "/Http2/GetCrossDepartmentApprovalRequests",
  });
};
/* 获取我的跨部门互评申请列表;*/
export const getApplyforList = async () => {
  return await request({
    url: "/Http2/GetMyAppliedCrossDepartmentRequests",
  });
};

/* 审批跨部门互评申请;*/
export const getApplyforListRequest = async (data: ACDRequest) => {
  return await request({
    url: "/Http2/ApproveCrossDepartmentRequest",
    data,
  });
};

//检查总分合理性
export const getCheckRationality = async () => {
  return await request({
    url: "/Http2/checkRationality",
  });
};

//截止日期
// 0 1 2 3 (5-8)  4 5(10 -12)
export const getDeadline = async () => {
  return await request({
    url: "/Http2/GetDeadline",
  });
};

//评分日期
export const getUpdateDeadline = async (data: UpdateDeadline) => {
  return await request({
    url: "/Http2/UpdateDeadline",
    data,
  });
};

//月 季 年评
export const getAppraisalStatistics = async (data: QueryType) => {
  return await request({ url: "/Http2/GetAppraisalStatistics", data });
};

//自评通知
export const getSelfNotice = async () => {
  return await request({ url: "/Http2/GetUsersNotSelfEvaluated" });
};

// 互评通知
export const getMutualNotice = async () => {
  return await request({ url: "/Http2/GetUsersNotMutuallyEvaluated" });
};

// 上对下通知
export const getToptoBottom = async () => {
  return await request({
    url: "/Http2/GetSupervisorsNotEvaluatedSubordinates",
  });
};

// 下对上通知
export const getBottomtoTop = async () => {
  return await request({
    url: "/Http2/GetSubordinatesNotEvaluatedSupervisors",
  });
};

/** 获取题目接口 */
export const getQuestions = async (data: {
  DepartmentId: string;
  TargetUserId: number;
}) => {
  return await request({ url: "/Http2/GetEvaluation", data });
};
/** 获取部门信息 --弃用 */
// export const getDepartmentInfo = async () => {
// 	return await request({url:'/Http2/GetUserList'})
// }
/** 获取全部部门 */
export const getDepartmentList = async () => {
  return await request({ url: "/Http2/GetUserList" });
};
/** 获取下一层部门信息 */
export const getDepartmentInfo = async (data: {
  ParentId: string | number;
  TId: string | number;
}) => {
  return await request({ url: "/Http2/GetDepartList", data });
};
/** 搜索用户信息 */
export const searchUserList = async (data: { UserName: string }) => {
  return await request({ url: "/Http2/GetUserDetail", data });
};
/** 提交评测信息 */
export const setAppraisal = async (data: SetAppraisal) => {
  return await request({ url: "/Http2/SetAppraisal", data });
};
/**
 * 获取历史评测
 * TimeDate传年份和月份类似于2024
 */
export const getHistoryList = async (data: { TimeDate: number }) => {
  return await request({ url: "/Http2/GetTimeline", data });
};

/**
 * 获取具体评测
 * TId 评测类型 :0,1,2,3
 * TimeDate传年份和月份类似于202410
 */
export const getAHistoryItem = async (data: {
  TId: number;
  TimeDate: string;
}) => {
  return await request({ url: "/Http2/GetAppraisal", data });
};
/** 获取登录用户信息 */
export const getUserInfo = async () => {
  return await request({ url: "/User/GetUser" });
};
/** 设置邮箱信息 */
export const setUserEmail = async (data: { Email: string }) => {
  return await request({ url: "/User/SetEmail", data });
};
/** 发送验证码  */
export const sendSms = async (data: {
  Mobile: string;
  SmsType: "findphone" | "findpwd";
}) => {
  return await request({ url: "/Http2/SendSms", data });
};
/** 换绑手机号 */
export const updatePhone = async (data: { Mobile: string; Code: number }) => {
  return await request({ url: "/User/UpdateMobile", data });
};
/** 修改密码 */
export const updatePassword = async (data: { pwd: string; pwd2: string }) => {
  return await request({ url: "/User/SetPwd", data });
};
/** 找回密码 */
export const findPassword = async (data: {
  Mobile: string;
  Password: string;
  SmsCode: number;
  ComfimPassword: string;
}) => {
  return await request({ url: "/Http2/RetrievePassword", data });
};
// 退出登录
export const getLogout = async (data: {
  AccessToken: string;
  RefreshToken: string;
}) => {
  return await request({ url: "/Http2/Logout" });
};
