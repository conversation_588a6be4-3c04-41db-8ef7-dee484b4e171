"use strict";
const common_vendor = require("../common/vendor.js");
require("../store/index.js");
const utils_request = require("./request.js");
const phoneRegex = /^(13[0-9]{9})|(18[0-9]{9})|(14[0-9]{9})|(17[0-9]{9})|(15[0-9]{9})$/;
async function getCurrentEnvironment() {
  var _a, _b, _c;
  const appBaseInfo = common_vendor.index.getAppBaseInfo();
  const platform = (_b = (_a = appBaseInfo == null ? void 0 : appBaseInfo.host) == null ? void 0 : _a.env) == null ? void 0 : _b.toLowerCase();
  if (platform === "wechat" || ((_c = appBaseInfo.hostName) == null ? void 0 : _c.toLowerCase()) === "wechat") {
    const http = utils_request.getBaseUrl();
    return http;
  } else {
    return "/api";
  }
}
const uniToast = (title = "网络请求失败") => common_vendor.index.showToast({
  icon: "none",
  duration: 3e3,
  title
});
const cantBeBad = (value) => {
  const http = utils_request.getBaseUrl().replace(/\Api\/V1$/, "");
  if (value != "" && value != void 0 && value != null) {
    value = http + value;
    return value;
  } else {
    return "../../static/images/avatar.png";
  }
};
function getCurrentDate() {
  const currentDate = /* @__PURE__ */ new Date();
  const year = currentDate.getFullYear();
  const month = String(currentDate.getMonth() + 1).padStart(2, "0");
  const day = String(currentDate.getDate()).padStart(2, "0");
  return `${year}/${month}/${day}`;
}
function generateHash(str) {
  const hash = common_vendor.CryptoJS.MD5(str).toString().toUpperCase();
  return hash;
}
function getTheYearMonths(year) {
  const currentDate = /* @__PURE__ */ new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;
  if (!year) {
    year = currentYear;
  }
  if (year !== void 0 && year !== currentYear) {
    return Array.from(
      { length: 12 },
      (_, index) => `${index + 1}`.length === 1 ? `${year}/0${index + 1}` : `${year}/${index + 1}`
    );
  } else {
    return Array.from(
      { length: currentMonth },
      (_, index) => `${index + 1}`.length === 1 ? `${year}/0${index + 1}` : `${year}/${index + 1}`
    );
  }
}
function getYearsDistanceFrom2024() {
  const years = [];
  const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
  for (let year = 2025; year <= currentYear; year++) {
    years.push(year);
  }
  return years;
}
function hidePhoneNumber(phoneNumber) {
  const hiddenPhoneNumber = phoneNumber.replace(
    /(\d{3})\d{5}(\d{2})/,
    "$1*****$2"
  );
  return hiddenPhoneNumber;
}
exports.cantBeBad = cantBeBad;
exports.generateHash = generateHash;
exports.getCurrentDate = getCurrentDate;
exports.getCurrentEnvironment = getCurrentEnvironment;
exports.getTheYearMonths = getTheYearMonths;
exports.getYearsDistanceFrom2024 = getYearsDistanceFrom2024;
exports.hidePhoneNumber = hidePhoneNumber;
exports.phoneRegex = phoneRegex;
exports.uniToast = uniToast;
