{
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    // {
    // 	"path" : "pages/login/views/register",
    // 	"style" :
    // 	{
    // 		"navigationBarTitleText" : "注册",
    // 		"enablePullDownRefresh" : false
    // 	}
    // },
    // {
    // 	"path" : "pages/login/views/acount/uploadIDCard",
    // 	"style" :
    // 	{
    // 		"navigationBarTitleText" : "上传身份资料",
    // 		"enablePullDownRefresh" : false
    // 	}
    // },
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页"
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationBarTitleText": "登录",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/work/work",
      "style": {
        "navigationBarTitleText": "工作台",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/mine/mine",
      "style": {
        "navigationBarTitleText": "我的",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/login/views/options",
      "style": {
        "navigationBarTitleText": "忘记密码选择",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/login/views/acount/findMyAccount",
      "style": {
        "navigationBarTitleText": "账号申诉",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/login/views/acount/checking",
      "style": {
        "navigationBarTitleText": "审核中",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/index/views/assessment",
      "style": {
        "navigationBarTitleText": "自我评价",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/index/views/submit",
      "style": {
        "navigationBarTitleText": "提交",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/index/views/department/index",
      "style": {
        "navigationBarTitleText": "选择部门",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/mine/views/setting",
      "style": {
        "navigationBarTitleText": "设置",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/work/views/history",
      "style": {
        "navigationBarTitleText": "历史评测",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/work/views/middlePage",
      "style": {
        "navigationBarTitleText": "选择",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/work/views/tablePage",
      "style": {
        "navigationBarTitleText": "评测表格",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/mine/views/userInfo",
      "style": {
        "navigationBarTitleText": "个人信息",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/mine/views/editPhone",
      "style": {
        "navigationBarTitleText": "换绑手机号",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/mine/views/editPassword",
      "style": {
        "navigationBarTitleText": "修改密码",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/componentPage/validatePhonePage",
      "style": {
        "navigationBarTitleText": "验证手机号",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/login/views/retrievePassword",
      "style": {
        "navigationBarTitleText": "找回密码",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/mine/views/receiveComments",
      "style": {
        "navigationBarTitleText": "收到评价",
        "enablePullDownRefresh": false
      }
    }
  ],
  "subPackages": [
    {
      "root": "pageA/pages",
      "pages": [
        {
          "path": "applicationList/applicationList",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "statistics/statistics",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "undone/undone",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "hr/hr",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "hr/time",
          "style": {
            "navigationBarTitleText": ""
          }
        }
      ]
    }
  ],
  "entryPagePath": "pages/index/index",
  "tabBar": {
    "backgroundColor": "#FFFFFF",
    "color": "#A1A1A1",
    "selectedColor": "#333333",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "./static/work/home0.png",
        "selectedIconPath": "./static/index/home1.png"
      },
      {
        "pagePath": "pages/work/work",
        "text": "工作台",
        "iconPath": "./static/index/work0.png",
        "selectedIconPath": "static/work/work1.png"
      },
      {
        "pagePath": "pages/mine/mine",
        "text": "我的",
        "iconPath": "static/index/mine0.png",
        "selectedIconPath": "static/mine/mine1.png"
      }
    ]
  },
  "globalStyle": {
    "navigationStyle": "custom",
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "easycom": {
    "autoscan": true,
    // 注意一定要放在custom里，否则无效，https://ask.dcloud.net.cn/question/131175
    "custom": {
      "^u--(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^up-(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue"
    }
  }
}
