"use strict";
const common_vendor = require("../../../../../../common/vendor.js");
if (!Array) {
  const _easycom_up_input2 = common_vendor.resolveComponent("up-input");
  const _easycom_up_form_item2 = common_vendor.resolveComponent("up-form-item");
  const _easycom_up_col2 = common_vendor.resolveComponent("up-col");
  const _easycom_up_row2 = common_vendor.resolveComponent("up-row");
  const _easycom_up_textarea2 = common_vendor.resolveComponent("up-textarea");
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  const _easycom_up_form2 = common_vendor.resolveComponent("up-form");
  (_easycom_up_input2 + _easycom_up_form_item2 + _easycom_up_col2 + _easycom_up_row2 + _easycom_up_textarea2 + _easycom_up_button2 + _easycom_up_form2)();
}
const _easycom_up_input = () => "../../../../../../node-modules/uview-plus/components/u-input/u-input.js";
const _easycom_up_form_item = () => "../../../../../../node-modules/uview-plus/components/u-form-item/u-form-item.js";
const _easycom_up_col = () => "../../../../../../node-modules/uview-plus/components/u-col/u-col.js";
const _easycom_up_row = () => "../../../../../../node-modules/uview-plus/components/u-row/u-row.js";
const _easycom_up_textarea = () => "../../../../../../node-modules/uview-plus/components/u-textarea/u-textarea.js";
const _easycom_up_button = () => "../../../../../../node-modules/uview-plus/components/u-button/u-button.js";
const _easycom_up_form = () => "../../../../../../node-modules/uview-plus/components/u-form/u-form.js";
if (!Math) {
  (_easycom_up_input + _easycom_up_form_item + _easycom_up_col + _easycom_up_row + _easycom_up_textarea + _easycom_up_button + _easycom_up_form)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "EvalForm",
  props: {
    formModel: {
      type: Object,
      required: true
    },
    rules: {
      type: Object,
      required: true
    },
    TIdNum: {
      type: Number,
      required: true
    }
  },
  emits: ["submit", "reset"],
  setup(__props, { expose: __expose }) {
    const inputBoxBottom = common_vendor.ref(0);
    const onKeyboardShow = (height) => {
      inputBoxBottom.value = height;
      document.body.style.overflow = "hidden";
    };
    const onKeyboardHide = () => {
      inputBoxBottom.value = 0;
      document.body.style.overflow = "auto";
    };
    const handleKeyboardHeightChange = (res) => {
      res.height > 0 ? onKeyboardShow(res.height) : onKeyboardHide();
    };
    common_vendor.onMounted(() => {
      common_vendor.index.onKeyboardHeightChange(handleKeyboardHeightChange);
    });
    common_vendor.onUnmounted(() => {
      common_vendor.index.offKeyboardHeightChange(handleKeyboardHeightChange);
    });
    const formRef = common_vendor.ref(null);
    __expose({
      validate: async () => {
        if (formRef.value) {
          return await formRef.value.validate();
        }
        return false;
      },
      resetFields: () => {
        if (formRef.value) {
          if (typeof formRef.value.resetFields === "function") {
            formRef.value.resetFields();
            console.log("resetFields");
          }
        }
      },
      clearValidate: () => {
        if (formRef.value) {
          if (typeof formRef.value.clearValidate === "function") {
            formRef.value.clearValidate();
            console.log("clearValidate");
          }
        }
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.TIdNum > 3
      }, __props.TIdNum > 3 ? {
        b: common_vendor.o(($event) => __props.formModel.formData.Subordinate = $event),
        c: common_vendor.p({
          disabled: true,
          placeholder: "请选择被评人",
          modelValue: __props.formModel.formData.Subordinate
        }),
        d: common_vendor.p({
          label: "被评人:",
          prop: "formData.Subordinate"
        }),
        e: common_vendor.p({
          span: "5",
          align: "top"
        })
      } : {}, {
        f: __props.TIdNum > 4
      }, __props.TIdNum > 4 ? {
        g: common_vendor.p({
          span: "1"
        })
      } : {}, {
        h: __props.TIdNum === 5
      }, __props.TIdNum === 5 ? {
        i: common_vendor.o(($event) => __props.formModel.formData.Evaluator = $event),
        j: common_vendor.p({
          disabled: true,
          placeholder: "请选择评价人",
          modelValue: __props.formModel.formData.Evaluator
        }),
        k: common_vendor.p({
          label: "评测人:",
          prop: "formData.Evaluator"
        }),
        l: common_vendor.p({
          span: "5",
          align: "top"
        })
      } : {}, {
        m: common_vendor.p({
          align: "top"
        }),
        n: common_vendor.o(($event) => __props.formModel.formData.Reason = $event),
        o: common_vendor.p({
          adjustPosition: false,
          placeholder: "请输入理由",
          modelValue: __props.formModel.formData.Reason
        }),
        p: `${inputBoxBottom.value}px`,
        q: inputBoxBottom.value > 0 ? 1 : "",
        r: common_vendor.p({
          prop: "formData.Reason",
          label: "理由:"
        }),
        s: common_vendor.p({
          span: "12"
        }),
        t: common_vendor.p({
          span: "2"
        }),
        v: common_vendor.o(($event) => _ctx.$emit("submit")),
        w: common_vendor.p({
          type: "primary",
          text: "提交"
        }),
        x: common_vendor.p({
          span: "3"
        }),
        y: common_vendor.p({
          span: "1"
        }),
        z: common_vendor.o(($event) => _ctx.$emit("reset")),
        A: common_vendor.p({
          plain: true,
          type: "primary",
          text: "重置"
        }),
        B: common_vendor.p({
          span: "3"
        }),
        C: common_vendor.p({
          customStyle: "margin: 10px"
        }),
        D: common_vendor.sr(formRef, "97744cf2-0", {
          "k": "formRef"
        }),
        E: common_vendor.p({
          labelPosition: "top",
          model: __props.formModel,
          rules: __props.rules
        })
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-97744cf2"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/index/views/department/CrossEvaluation/components/EvalForm.vue"]]);
wx.createComponent(Component);
