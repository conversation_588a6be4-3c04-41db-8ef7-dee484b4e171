
.main.data-v-5468c562 {
  background-color: #f7f7f7;
  padding: 10rpx 0rpx;
}
.content.data-v-5468c562 {
  width: 100%;
  padding: 30rpx;
  display: flex;
  background-color: white;
  border-bottom: 1px solid #eef1fe;
}
.content.data-v-5468c562:nth-child(1) {
  padding: 30rpx 20rpx 30rpx 30rpx;
  height: 140rpx;
  display: flex;
  place-items: center;
  margin: 10rpx 0rpx;
}
.value.data-v-5468c562 {
  color: #888888;
  margin-left: auto;
  margin-right: 20rpx;
  /* border: 2px solid ; */
}
.content:nth-child(1) > .value.data-v-5468c562 {
  margin-right: 0rpx !important;
}
.content:nth-child(3) > .value.data-v-5468c562 {
  min-width: 380rpx;
  text-align: right;
}
.specialBtn.data-v-5468c562 {
  background-color: white;
  border: none;
  box-shadow: none;
  text-align: right;
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
  color: #888888 !important;
  padding: 0;
  text-decoration: none;
  height: 100%;
  /* border: 2px solid ; */
  display: flex;
  place-items: center;
  justify-content: right;
  border-radius: 50%;
}
.specialBtn.data-v-5468c562::after {
  border: none;
}
.input.data-v-5468c562 {
  /* 	max-width: 200rpx;
		min-width: 300rpx; */
  padding: 0px 0rpx 0px 20rpx;
  text-overflow: ellipsis;
  /* border: 1px solid ; */
}
.avatar.data-v-5468c562 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 5px;
  overflow: hidden;
  background-color: #ccc;
  box-shadow: 0 0 4px #8b95ed;
  border: 1px solid skyblue;
}
.avatar > image.data-v-5468c562 {
  width: 100%;
  height: 100%;
}
