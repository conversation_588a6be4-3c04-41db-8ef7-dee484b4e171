<view class="time delHead data-v-1a5e0555"><head wx:if="{{a}}" class="data-v-1a5e0555" u-i="1a5e0555-0" bind:__l="__l" u-p="{{a}}"></head><view class="time-form data-v-1a5e0555"><view class="tip data-v-1a5e0555"> *请注意日期依次从小到大填写 5{{b}} 8 {{c}} 10 {{d}} 12 </view><up-form wx:if="{{x}}" class="r data-v-1a5e0555" u-s="{{['d']}}" u-r="formRef" u-i="1a5e0555-1" bind:__l="__l" u-p="{{x}}"><up-form-item wx:if="{{g}}" class="data-v-1a5e0555" u-s="{{['d']}}" u-i="1a5e0555-2,1a5e0555-1" bind:__l="__l" u-p="{{g}}"><up-input wx:if="{{f}}" class="data-v-1a5e0555" u-i="1a5e0555-3,1a5e0555-2" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"/></up-form-item><up-form-item wx:if="{{j}}" class="data-v-1a5e0555" u-s="{{['d']}}" u-i="1a5e0555-4,1a5e0555-1" bind:__l="__l" u-p="{{j}}"><up-input wx:if="{{i}}" class="data-v-1a5e0555" u-i="1a5e0555-5,1a5e0555-4" bind:__l="__l" bindupdateModelValue="{{h}}" u-p="{{i}}"/></up-form-item><up-form-item wx:if="{{m}}" class="data-v-1a5e0555" u-s="{{['d']}}" u-i="1a5e0555-6,1a5e0555-1" bind:__l="__l" u-p="{{m}}"><up-input wx:if="{{l}}" class="data-v-1a5e0555" u-i="1a5e0555-7,1a5e0555-6" bind:__l="__l" bindupdateModelValue="{{k}}" u-p="{{l}}"/></up-form-item><up-form-item wx:if="{{p}}" class="data-v-1a5e0555" u-s="{{['d']}}" u-i="1a5e0555-8,1a5e0555-1" bind:__l="__l" u-p="{{p}}"><up-input wx:if="{{o}}" class="data-v-1a5e0555" u-i="1a5e0555-9,1a5e0555-8" bind:__l="__l" bindupdateModelValue="{{n}}" u-p="{{o}}"/></up-form-item><up-row wx:if="{{v}}" class="data-v-1a5e0555" u-s="{{['d']}}" u-i="1a5e0555-10,1a5e0555-1" bind:__l="__l" u-p="{{v}}"><up-col wx:if="{{q}}" class="data-v-1a5e0555" u-i="1a5e0555-11,1a5e0555-10" bind:__l="__l" u-p="{{q}}"/><up-col wx:if="{{t}}" class="data-v-1a5e0555" u-s="{{['d']}}" u-i="1a5e0555-12,1a5e0555-10" bind:__l="__l" u-p="{{t}}"><up-button wx:if="{{s}}" class="data-v-1a5e0555" bindclick="{{r}}" u-i="1a5e0555-13,1a5e0555-12" bind:__l="__l" u-p="{{s}}"/></up-col></up-row></up-form></view><toast class="r data-v-1a5e0555" u-r="ref_toast" u-i="1a5e0555-14" bind:__l="__l"></toast></view>