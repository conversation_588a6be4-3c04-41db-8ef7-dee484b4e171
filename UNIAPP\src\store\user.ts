import { defineStore } from "pinia";
import type { DepartmentUser } from "@/types";
import { reactive } from "vue";
// 第一个参数是应用程序中 store 的唯一 id
// 第二个参数是配置对象

export const useUserStore = defineStore("user", {
  // state是一个函数，返回一个对象
  state: () => {
    return {
      DeveloperList: reactive([""]),
      type: 1,
      IsDepartmentlanager: false,
      userInfo: reactive<DepartmentUser>({
        ID: 1,
        DisplayName: "未知用户",
        Sex: 1,
        Ex4: "销售部",
        Avatar: "",
      }),
      /** 清除当前登录用户信息 */
      resetLoginData: function () {
        // 清除当前登录用户信息
        this.userInfo = {
          ID: 1,
          DisplayName: "未知用户",
          Sex: 1,
          Ex4: "销售部",
          Avatar: "",
        };
        // 清除本地缓存
        uni.clearStorageSync();
      },
    };
  },
  getters: {
    /** 切换菜单 传入 name */
    // resetLoginData(state: any) {
    //   return (name: string) => {
    //   };
    // },
  },
  actions: {},
});
