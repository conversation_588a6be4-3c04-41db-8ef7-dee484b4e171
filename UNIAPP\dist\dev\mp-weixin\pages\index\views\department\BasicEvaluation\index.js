"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const store_user = require("../../../../../store/user.js");
const store_index = require("../../../../../store/index.js");
const utils_http = require("../../../../../utils/http.js");
require("../../../../../utils/request.js");
require("../../../../../utils/index.js");
require("../../../../../store/pinia.js");
require("../../../../../utils/sign.js");
require("../../../../../utils/sha1.js");
if (!Math) {
  (UserList + toast)();
}
const UserList = () => "./components/UserList.js";
const toast = () => "../../../../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const store = store_index.useIndexStore();
    const ref_toast = common_vendor.ref();
    const searchValue = common_vendor.ref("");
    const list = common_vendor.ref([]);
    const TIdNum = common_vendor.ref(store.submitData.TId);
    const parentId = common_vendor.ref(0);
    const idx = common_vendor.ref(0);
    const historyStack = common_vendor.ref([
      { id: parentId.value }
    ]);
    const getDepartmentInfoAPI = async (id) => {
      var _a;
      const res = await utils_http.getDepartmentInfo({
        ParentId: id,
        TId: TIdNum.value
      });
      let data = res.data.Data.listjson;
      common_vendor.index.hideLoading();
      if (id === 0) {
        list.value = data;
        return;
      }
      list.value = ((_a = data[idx.value]) == null ? void 0 : _a.Users) || [];
    };
    const handleSelect = async (item, index) => {
      idx.value = index;
      if (TIdNum.value === 0) {
        getDepartmentInfoAPI(parentId.value);
        goEvaluation(item);
        return;
      }
      if (item.DepartmentId) {
        parentId.value = item.DepartmentId;
        historyStack.value.push({ id: parentId.value });
        getDepartmentInfoAPI(parentId.value);
      } else {
        goEvaluation(item);
      }
    };
    const goEvaluation = (item) => {
      if (item.DisplayName === userStore.userInfo.DisplayName) {
        common_vendor.index.showToast({
          title: "无法在当前页面对自己评价哦",
          icon: "none"
        });
        return;
      }
      store.routerUserinfo = item;
      if (TIdNum.value === 0) {
        store.routerUserinfo.DepartmentId = item.Id;
      } else {
        store.routerUserinfo.DepartmentId = historyStack.value[historyStack.value.length - 1].id;
      }
      common_vendor.index.navigateTo({
        url: "/pages/index/views/assessment"
      });
    };
    const back = async () => {
      if (historyStack.value.length > 1) {
        historyStack.value.pop();
        const prevLevel = historyStack.value[historyStack.value.length - 1];
        parentId.value = prevLevel.id;
        await getDepartmentInfoAPI(prevLevel.id);
      }
    };
    common_vendor.onLoad(() => {
      getDepartmentInfoAPI(0);
    });
    common_vendor.onShow(() => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: historyStack.value.length > 1 && parentId.value != 0
      }, historyStack.value.length > 1 && parentId.value != 0 ? {
        b: common_vendor.o(($event) => back())
      } : {}, {
        c: common_vendor.o(handleSelect),
        d: common_vendor.p({
          list: list.value,
          TIdNum: TIdNum.value
        }),
        e: !list.value.length && searchValue.value.length,
        f: common_vendor.sr(ref_toast, "40206502-1", {
          "k": "ref_toast"
        })
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-40206502"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/index/views/department/BasicEvaluation/index.vue"]]);
wx.createComponent(Component);
