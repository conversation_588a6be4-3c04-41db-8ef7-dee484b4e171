"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const utils_index = require("../../../utils/index.js");
const utils_http = require("../../../utils/http.js");
const store_user = require("../../../store/user.js");
require("../../../utils/request.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  const _easycom_up_input2 = common_vendor.resolveComponent("up-input");
  (_easycom_up_icon2 + _easycom_up_button2 + _easycom_up_input2)();
}
const _easycom_up_icon = () => "../../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_button = () => "../../../node-modules/uview-plus/components/u-button/u-button.js";
const _easycom_up_input = () => "../../../node-modules/uview-plus/components/u-input/u-input.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_button + _easycom_up_input + toast)();
}
const toast = () => "../../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "editPhone",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const ref_toast = common_vendor.ref();
    const list = common_vendor.ref([
      { name: "手机号", value: "", text: "请输入新手机号" },
      { name: "验证码", value: "", text: "请输入验证码" }
    ]);
    const codeText = common_vendor.ref("获取验证码");
    const store = store_index.useIndexStore();
    const getCode = async () => {
      var _a;
      if (codeText.value == "获取验证码" || codeText.value == "重新发送") {
        await utils_http.sendSms({ Mobile: list.value[0].value, SmsType: "findphone" }).then((res) => {
          var _a2, _b;
          if (res.data.Code != 1) {
            (_a2 = ref_toast.value) == null ? void 0 : _a2.info(res.data.Message);
            return;
          }
          (_b = ref_toast.value) == null ? void 0 : _b.success("发送成功");
          codeText.value = "已发送";
          timerFN(60);
        });
      } else {
        (_a = ref_toast.value) == null ? void 0 : _a.warning("请勿重复获取");
      }
    };
    const timerFN = (time = 60) => {
      let timer = setInterval(() => {
        time -= 1;
        codeText.value = "已发送：" + time;
        if (time <= 0) {
          clearInterval(timer);
          codeText.value = "重新发送";
        }
      }, 1e3);
    };
    const Submit = () => {
      var _a, _b, _c;
      if (!utils_index.phoneRegex.test(list.value[0].value)) {
        (_a = ref_toast.value) == null ? void 0 : _a.warning("请填写正确的手机号");
        return;
      }
      if (codeText.value === "获取验证码") {
        (_b = ref_toast.value) == null ? void 0 : _b.warning("请点击获取验证码");
        return;
      }
      if (!list.value[1].value) {
        (_c = ref_toast.value) == null ? void 0 : _c.warning("请填写验证码");
        return;
      }
      common_vendor.index.showLoading({
        title: "短信校验中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        handleSubmit({ Mobile: list.value[0].value, Code: Number(list.value[1].value) });
      }, 2e3);
    };
    const handleSubmit = async (e) => {
      await utils_http.updatePhone(e).then((res) => {
        var _a, _b;
        console.log("换绑手机情况 -->", res);
        if (res.data.Code != 1) {
          (_a = ref_toast.value) == null ? void 0 : _a.fail(res.data.Message);
          return;
        }
        userStore.userInfo.Mobile = list.value[0].value;
        (_b = ref_toast.value) == null ? void 0 : _b.success("换绑成功");
        setTimeout(() => {
          common_vendor.index.navigateBack({
            delta: 1
          });
        }, 1500);
      });
    };
    const none = () => {
      var _a;
      (_a = ref_toast.value) == null ? void 0 : _a.info("请联系管理员");
    };
    common_vendor.onLoad((e) => {
      list.value[0].value = e.data;
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => common_vendor.unref(store).backAPage()),
        b: common_vendor.f(list.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.name),
            b: index != 0
          }, index != 0 ? {} : {}, {
            c: index === 1
          }, index === 1 ? common_vendor.e({
            d: common_vendor.t(codeText.value),
            e: codeText.value === "获取验证码" || codeText.value === "重新发送"
          }, codeText.value === "获取验证码" || codeText.value === "重新发送" ? {
            f: "660c5b28-2-" + i0 + "," + ("660c5b28-1-" + i0),
            g: common_vendor.p({
              name: "email",
              size: "32",
              color: "#2979ff"
            })
          } : {}, {
            h: common_vendor.o(($event) => getCode(), index),
            i: "660c5b28-1-" + i0 + "," + ("660c5b28-0-" + i0),
            j: common_vendor.p({
              type: "primary",
              plain: true,
              size: "small"
            })
          }) : {}, {
            k: "660c5b28-0-" + i0,
            l: common_vendor.o(($event) => item.value = $event, index),
            m: common_vendor.p({
              type: "text",
              border: "none",
              placeholder: item.text,
              modelValue: item.value
            }),
            n: index
          });
        }),
        c: common_vendor.o(($event) => none()),
        d: common_vendor.o(($event) => Submit()),
        e: common_vendor.sr(ref_toast, "660c5b28-3", {
          "k": "ref_toast"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-660c5b28"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/mine/views/editPhone.vue"]]);
wx.createPage(MiniProgramPage);
