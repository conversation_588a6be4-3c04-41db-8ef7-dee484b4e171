<template>
	<view class="pageBox">
		<view class="head">
		    <view class="return" @click="returnSetp"> 
				<image class="return-img" src="../../../static/icons/fanhui.png" ></image>
			</view>
		    <text class="title">忘记密码</text>
		</view>
		
		<view class="bg-white text">
		
			您的手机号，
			<view style="text-indent: 100rpx;">
				是否能收到验证码?
			</view>
		
		</view>
		
		<view class="btn_box">
			<button class="button1" @click="onSelect(0)">不能</button>
			<button class="button2" @click="onSelect(1)">能</button>
		</view>
		
		<toast ref="ref_toast"></toast>
	</view>
</template>

<script lang="ts" setup>
	import { onShow } from '@dcloudio/uni-app';
	import toast from '../../../components/toast.vue'
	import type { IToast } from '@/types';
	import { ref } from 'vue';
	const ref_toast = ref<IToast>()
	const returnSetp = ()=>{
		uni.navigateBack({
			delta: 1
		})
	}
	const onSelect = (val:number) =>{
		if(val === 1){
			uni.redirectTo({
				url:'./retrievePassword'
			})
			return;
		}
		ref_toast.value?.info('请联系管理员')
	}
	//
	onShow((e)=>{
		// console.log(e);
	})
</script>

<style scoped lang="scss">
	.text{
		margin: 20rpx 20rpx 40rpx 20rpx;
		width: fit-content;
		height: auto;
		// border: 1px solid ;
		font-family: PingFang SC;
		font-size: 40rpx;
		color: #383A49;
		line-height: 73rpx;
		// margin:0px auto;
		text-indent: 40rpx;
	}
	.btn_box{
		width: 100%;
		height: fit-content;
		padding-top: 40rpx;
		display: flex;
		justify-content: space-around;
	}
	.button1{
		width: 300rpx;
		height: 84rpx;
		background: #E6E6E6;
		box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0,0,0,0.1);
		border-radius: 0rpx 42rpx 0rpx 42rpx;
	}
	.button2{
		width: 300rpx;
		height: 84rpx;
		background: linear-gradient(80deg, #677BF0, #2330DF);
		box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0,0,0,0.2);
		border-radius: 42rpx 0rpx 42rpx 0rpx;
		color: white;
	}
</style>