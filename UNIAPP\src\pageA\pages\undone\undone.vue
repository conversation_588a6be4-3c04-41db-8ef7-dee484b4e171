<template>
  <view class="undone delHead">
    <Head :title="data.title" returnType="home"></Head>
    <view class="list">
      <up-collapse accordion>
        <up-collapse-item
          v-for="(t, i) in data.originList"
          :key="i"
          :disabled="data.id != 2"
          :title="onTitle(t)">
          <template v-slot:right-icon>
            <text
              class="deg"
              v-if="
                t.NotEvaluatedUsersCount || t.NotEvaluatedSubordinates?.length
              ">
              {{
                t.NotEvaluatedUsersCount ||
                t.NotEvaluatedSubordinates.length ||
                ""
              }}
            </text>
          </template>
          <view
            class="content"
            v-for="(v, k) in t.NotEvaluatedSubordinates"
            :key="k"
            v-if="t.NotEvaluatedSubordinates?.length">
            <text class="u-collapse-content">{{ v.DepartmentName }}</text>
            <br />
            <text class="u-collapse-content">{{ v.DisplayName }}</text>
          </view>
        </up-collapse-item>
      </up-collapse>
      <view :class="{ empty: data.textList?.length > 14 }"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Head from "@/components/head.vue";
import { onBackPress, onLoad } from "@dcloudio/uni-app";
const data = ref([]);
onLoad((e: any) => {
  // console.log("e :>> ", e.data);
  if (e.data) {
    data.value = JSON.parse(decodeURIComponent(e.data));
    // console.log("data.value", data.value);

  }
});

const onTitle = (t: any) => {
  const Id = data.value.id;
  if (Id == 0) {
    return t.DisplayName;
  }
  if (Id == 1) {
    return t.UserName;
  }
  if (Id == 2) {
    return t.SupervisorName;
  }
  if (Id == 3) {
    return t.SubordinateName;
  }
};
</script>

<style scoped lang="scss">
.undone {
  :deep(.u-cell__body) {
    padding: 20rpx !important;
    font-size: 28rpx !important;
  }

  .content {
    padding: 10rpx 0;
    border-bottom: 1px solid #cccccc;
  }

  .content:last-child {
    border-bottom: 1px solid transparent;
  }

  .deg {
    padding: 0rpx 8rpx;
    border-radius: 50%;
    color: red;
  }

  .u-collapse-content {
    font-size: 14px;
  }
  .empty {
    width: 100%;
    height: 60rpx;
    background-color: #ffffff;
  }
}
</style>
