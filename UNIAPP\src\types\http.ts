/** 登录-接口 */
export interface Login {
  mobile: string;
  password: string;
}

export interface Assign {
  //  评价人ID（其他部门人员）
  EvaluatorId: string | number;
  // 被评价人ID（主管的下属）
  SubordinateId: string | number;
  // 被评价人所在部门ID;
  DepartmentId: string | number;
  // 指定理由;
  Reason: string;
  // 语言标识符;
  Lng?: string;
  // 被评价人ID
  BId?: string | number;
}

/** 审批跨部门互评申请-接口 */
export interface ACDRequest {
  // 申请ID
  RequestId: string | number;
  // 审批状态: 1表示同意，2表示拒绝
  Status: 0 | 1 | 2;
  // 审批备注
  Remark: string;
  // 语言标识符
  Lng?: string;
}

export interface QueryType {
  TimeDate: string;
  // "month" | "quarter" | "year"
  QueryType: string;
}

export interface UpdateDeadline {
  SubmitDay: number;
  AssignDay: number;
  UrgedDay: number;
  CompleteDay: number;
}
