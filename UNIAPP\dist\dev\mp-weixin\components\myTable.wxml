<view wx:if="{{a}}" class="my-table data-v-608e08cb"><view class="table-header data-v-608e08cb"><view class="table-row data-v-608e08cb"><view wx:for="{{b}}" wx:for-item="column" wx:key="b" class="table-cell header-cell data-v-608e08cb" style="{{'flex:' + column.c}}">{{column.a}}</view></view></view><view class="table-body data-v-608e08cb"><view wx:for="{{c}}" wx:for-item="row" wx:key="b" class="{{['table-row', 'data-v-608e08cb', row.c && 'row-stripe']}}"><view wx:for="{{row.a}}" wx:for-item="column" wx:key="b" class="table-cell data-v-608e08cb" style="{{'flex:' + column.c}}">{{column.a}}</view></view></view></view><no-data wx:if="{{d}}" class="data-v-608e08cb" u-i="608e08cb-0" bind:__l="__l" u-p="{{d}}"></no-data>