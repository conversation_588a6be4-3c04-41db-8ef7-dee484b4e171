<template>
  <view class="eval-form">
    <up-form
      labelPosition="top"
      :model="formModel"
      :rules="rules"
      ref="formRef">
      <up-row align="top">
        <up-col span="5" v-if="TIdNum > 3" align="top">
          <up-form-item label="被评人:" prop="formData.Subordinate">
            <up-input
              v-model="formModel.formData.Subordinate"
              disabled
              placeholder="请选择被评人" />
          </up-form-item>
        </up-col>
        <up-col span="1" v-if="TIdNum > 4" />
        <up-col span="5" v-if="TIdNum === 5" align="top">
          <up-form-item label="评测人:" prop="formData.Evaluator">
            <up-input
              v-model="formModel.formData.Evaluator"
              disabled
              placeholder="请选择评价人" />
          </up-form-item>
        </up-col>
      </up-row>
      <up-row>
        <up-col span="12">
          <up-form-item prop="formData.Reason" label="理由:">
            <view   :style="{ bottom: `${inputBoxBottom}px` }"
            :class="{'textarea-wrapper': inputBoxBottom > 0}" >
              <up-textarea
              :adjustPosition="false"
                v-model="formModel.formData.Reason"
                placeholder="请输入理由" />
            </view>
          </up-form-item>
        </up-col>
      </up-row>

      <up-row customStyle="margin: 10px">
        <up-col span="2" />
        <up-col span="3">
          <up-button type="primary" @click="$emit('submit')" text="提交" />
        </up-col>
        <up-col span="1" />
        <up-col span="3">
          <up-button
            :plain="true"
            type="primary"
            @click="$emit('reset')"
            text="重置" />
        </up-col>
      </up-row>
    </up-form>
  </view>
</template>

<script setup lang="ts">
import { onLoad } from "@dcloudio/uni-app";
import { ref, onMounted, onUnmounted } from "vue";

const inputBoxBottom = ref(0);

// 键盘显示处理
const onKeyboardShow = (height: number) => {
  inputBoxBottom.value = height;
  // 防止页面滚动
  document.body.style.overflow = 'hidden';
  // console.log("inputBoxBottom.value :>> ", inputBoxBottom.value);
};

// 键盘隐藏处理
const onKeyboardHide = () => {
  inputBoxBottom.value = 0;
  // 恢复页面滚动
  document.body.style.overflow = 'auto';
};

// 统一处理键盘高度变化
const handleKeyboardHeightChange = (res: { height: number }) => {
  // console.log("键盘", res);
  res.height > 0 ? onKeyboardShow(res.height) : onKeyboardHide();
};

// 组件挂载时设置监听
onMounted(() => {
  uni.onKeyboardHeightChange(handleKeyboardHeightChange);
});

// 组件卸载时移除监听
onUnmounted(() => {
  uni.offKeyboardHeightChange(handleKeyboardHeightChange);
});

interface FormInstance {
  validate: () => Promise<boolean>;
  resetFields?: () => void;
  clearValidate?: () => void;
}

const formRef = ref<FormInstance | null>(null);

defineProps({
  formModel: {
    type: Object,
    required: true,
  },
  rules: {
    type: Object,
    required: true,
  },
  TIdNum: {
    type: Number,
    required: true,
  },
});

defineEmits(["submit", "reset"]);
// 暴露方法给父组件
defineExpose({
  validate: async () => {
    if (formRef.value) {
      return await formRef.value.validate();
    }
    return false;
  },
  resetFields: () => {
    if (formRef.value) {
      // 如果有resetFields方法，则调用
      if (typeof formRef.value.resetFields === "function") {
        formRef.value.resetFields();
        console.log("resetFields");
      }
    }
  },
  clearValidate: () => {
    if (formRef.value) {
      // 如果有clearValidate方法，则调用
      if (typeof formRef.value.clearValidate === "function") {
        formRef.value.clearValidate();
        console.log("clearValidate");
      }
    }
  },
});
</script>

<style lang="scss" scoped>
.eval-form {
  padding: 20rpx;
  background: #fff;
  border-top: 1px solid #eee;
}
.textarea-wrapper {
  position: fixed;  // 改为 fixed 定位
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #fff;  // 添加背景色
  padding: 20rpx;  // 添加内边距
  box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.05);  // 添加阴影效果
}

</style>
