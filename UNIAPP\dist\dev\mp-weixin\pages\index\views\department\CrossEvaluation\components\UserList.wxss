/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-list.data-v-67e069a4 {
  width: 100%;
}
.cu-chat[data-selected=true].data-v-67e069a4 {
  background-color: red;
}
.cu-item.data-v-67e069a4 {
  padding: 10rpx;
  border-bottom: 1px solid #eef1fe;
  display: flex;
}
.row.data-v-67e069a4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #666666;
  font-size: 28rpx;
  flex: 1;
}
.row .row-left.data-v-67e069a4 {
  width: 320rpx;
}
.row .row-right.data-v-67e069a4 {
  width: 240rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.row .nameBox.data-v-67e069a4 {
  display: flex;
  align-items: center;
}
.row .tagW.data-v-67e069a4 {
  margin-top: 4px;
  margin-left: 4px;
}
.row.data-v-67e069a4 .u-tag {
  margin-top: 10rpx !important;
  margin-right: 10rpx !important;
}
.picBox.data-v-67e069a4 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.pic.data-v-67e069a4 {
  border-radius: 50%;
  width: 40px;
  height: 40px !important;
  display: flex;
  margin-right: 14rpx;
}
.department.data-v-67e069a4 {
  color: #8799a3;
  margin-top: 10rpx;
  font-size: 22rpx;
}
.noData.data-v-67e069a4 {
  width: 100%;
  margin-top: 20rpx;
}