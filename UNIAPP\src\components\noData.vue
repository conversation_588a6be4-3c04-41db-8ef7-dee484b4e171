<template>
  <view class="noData" v-if="!data.length">
    <view class="pic">
      <image src="@/static/index/noData.png" mode="aspectFit"></image>
    </view>
    <view class="wen">
      无数据哦
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

</script>

<style lang="scss" scoped>
.pic {
  width: 90%;
  margin-left: 5%;
}

.wen {
  width: 100%;
  text-align: center;
  margin-top: 20rpx;
  color: dodgerblue;
}
</style>
