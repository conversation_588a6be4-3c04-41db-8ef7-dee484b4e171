"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const store_pinia = require("./store/pinia.js");
require("./utils/request.js");
require("./utils/index.js");
require("./store/index.js");
require("./store/user.js");
require("./utils/http.js");
require("./utils/sign.js");
require("./utils/sha1.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/login/login.js";
  "./pages/work/work.js";
  "./pages/mine/mine.js";
  "./pages/login/views/options.js";
  "./pages/login/views/acount/findMyAccount.js";
  "./pages/login/views/acount/checking.js";
  "./pages/index/views/assessment.js";
  "./pages/index/views/submit.js";
  "./pages/index/views/department/index.js";
  "./pages/mine/views/setting.js";
  "./pages/work/views/history.js";
  "./pages/work/views/middlePage.js";
  "./pages/work/views/tablePage.js";
  "./pages/mine/views/userInfo.js";
  "./pages/mine/views/editPhone.js";
  "./pages/mine/views/editPassword.js";
  "./pages/componentPage/validatePhonePage.js";
  "./pages/login/views/retrievePassword.js";
  "./pages/mine/views/receiveComments.js";
  "./pageA/pages/applicationList/applicationList.js";
  "./pageA/pages/statistics/statistics.js";
  "./pageA/pages/undone/undone.js";
  "./pageA/pages/hr/hr.js";
  "./pageA/pages/hr/time.js";
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "App",
  setup(__props) {
    common_vendor.onLaunch(() => {
    });
    common_vendor.onShow(() => {
    });
    common_vendor.onHide(() => {
    });
    return () => {
    };
  }
});
const App = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/App.vue"]]);
common_vendor.setConfig({
  // 修改$u.config对象的属性
  config: {
    // 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'
    unit: "rpx"
  },
  // 修改$u.props对象的属性
  props: {
    // 修改radio组件的size参数的默认值，相当于执行 uni.$u.props.radio.size = 30
    radio: {
      size: 15
    }
    // 其他组件属性配置
    // ......
  }
});
store_pinia.pinia.use(common_vendor.src_default);
function createApp() {
  const app = common_vendor.createSSRApp(App);
  app.use(common_vendor.uviewPlus);
  app.use(store_pinia.pinia);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
