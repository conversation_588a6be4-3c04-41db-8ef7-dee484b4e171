"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const store_user = require("../../../store/user.js");
const utils_http = require("../../../utils/http.js");
require("../../../utils/request.js");
require("../../../utils/index.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  _easycom_up_icon2();
}
const _easycom_up_icon = () => "../../../node-modules/uview-plus/components/u-icon/u-icon.js";
if (!Math) {
  _easycom_up_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "setting",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const list = common_vendor.ref([
      { name: "修改密码", icon: "email" },
      { name: "更换手机号", icon: `${userStore.userInfo.Mobile}` },
      { name: "退出登录", icon: "" }
    ]);
    const store = store_index.useIndexStore();
    common_vendor.onShow(() => {
      list.value[1].icon = userStore.userInfo.Mobile ?? "";
    });
    const getLogoutAPI = async () => {
      ({
        AccessToken: common_vendor.index.getStorageSync("AccessToken"),
        RefreshToken: common_vendor.index.getStorageSync("RefreshToken")
      });
      const res = await utils_http.getLogout();
      console.log("res", res);
      common_vendor.index.hideLoading();
      const code = res.data.Code;
      if (code === 1) {
        common_vendor.index.showToast({
          title: "退出成功",
          icon: "none"
        });
      } else {
        common_vendor.index.showToast({
          title: "退出失败",
          icon: "none"
        });
      }
    };
    const toRouter = (index) => {
      switch (index) {
        case 0:
          common_vendor.index.navigateTo({
            url: "./editPassword"
          });
          return;
        case 1:
          common_vendor.index.navigateTo({
            url: "./editPhone"
          });
          return;
        case 2:
          common_vendor.index.showActionSheet({
            itemList: [`确认登出，为您安全退出账号`, `并清除敏感信息`],
            itemColor: "#772E40",
            success: function(res) {
              getLogoutAPI();
              setTimeout(() => {
                userStore.resetLoginData();
                common_vendor.index.navigateTo({
                  url: "../../login/login"
                });
              }, 1e3);
            }
          });
          return;
        default:
          return;
      }
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => common_vendor.unref(store).backAPage()),
        b: common_vendor.f(list.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.name),
            b: index === 1
          }, index === 1 ? {
            c: common_vendor.t(item.icon)
          } : {
            d: "fd310691-0-" + i0,
            e: common_vendor.p({
              name: item.icon,
              size: "50"
            })
          }, {
            f: index,
            g: common_vendor.o(($event) => toRouter(index), index)
          });
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-fd310691"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/mine/views/setting.vue"]]);
wx.createPage(MiniProgramPage);
