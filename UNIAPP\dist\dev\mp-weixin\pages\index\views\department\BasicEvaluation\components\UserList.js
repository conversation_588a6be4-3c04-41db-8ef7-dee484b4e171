"use strict";
const common_vendor = require("../../../../../../common/vendor.js");
const utils_request = require("../../../../../../utils/request.js");
require("../../../../../../utils/index.js");
require("../../../../../../store/index.js");
require("../../../../../../store/user.js");
require("../../../../../../store/pinia.js");
require("../../../../../../utils/http.js");
require("../../../../../../utils/sign.js");
require("../../../../../../utils/sha1.js");
if (!Array) {
  const _easycom_up_tag2 = common_vendor.resolveComponent("up-tag");
  const _easycom_up_collapse2 = common_vendor.resolveComponent("up-collapse");
  (_easycom_up_tag2 + _easycom_up_collapse2)();
}
const _easycom_up_tag = () => "../../../../../../node-modules/uview-plus/components/u-tag/u-tag.js";
const _easycom_up_collapse = () => "../../../../../../node-modules/uview-plus/components/u-collapse/u-collapse.js";
if (!Math) {
  (_easycom_up_tag + _easycom_up_collapse)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "UserList",
  props: {
    list: {
      type: Array,
      default: () => []
    },
    TIdNum: {
      type: Number,
      required: true
    },
    selectedSubordinate: {
      type: String,
      default: ""
    }
  },
  emits: ["select", "selectSubordinate", "selectEvaluator"],
  setup(__props, { emit: __emit }) {
    const http = utils_request.getBaseUrl().replace(/\Api\/V1$/, "");
    const emit = __emit;
    const handleSelect = (item, index) => {
      emit("select", item, index);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.list.length
      }, __props.list.length ? {
        b: common_vendor.f(__props.list, (item, index, i0) => {
          return common_vendor.e({
            a: item.ID != 1
          }, item.ID != 1 ? common_vendor.e({
            b: item.Avatar ? common_vendor.unref(http) + item.Avatar : "../../../../../../static/images/avatar.png",
            c: common_vendor.t(item.DisplayName || item.FullPathName || item.Name),
            d: item.IsManager
          }, item.IsManager ? {
            e: "ebdf46e1-1-" + i0 + ",ebdf46e1-0",
            f: common_vendor.p({
              text: "主管",
              plain: true,
              size: "mini",
              type: "warning"
            })
          } : {}, {
            g: common_vendor.t(item.Ex4),
            h: __props.TIdNum > 3 ? 1 : "",
            i: __props.TIdNum > 3
          }, __props.TIdNum > 3 ? common_vendor.e({
            j: item.DisplayName && !__props.selectedSubordinate
          }, item.DisplayName && !__props.selectedSubordinate ? {
            k: common_vendor.o(($event) => _ctx.$emit("selectSubordinate", item), index),
            l: "ebdf46e1-2-" + i0 + ",ebdf46e1-0",
            m: common_vendor.p({
              text: "被评人",
              type: "error"
            })
          } : {}, {
            n: item.DisplayName && __props.TIdNum > 4 && __props.selectedSubordinate
          }, item.DisplayName && __props.TIdNum > 4 && __props.selectedSubordinate ? {
            o: common_vendor.o(($event) => _ctx.$emit("selectEvaluator", item), index),
            p: "ebdf46e1-3-" + i0 + ",ebdf46e1-0",
            q: common_vendor.p({
              text: "评测人",
              type: "success"
            })
          } : {}) : {}) : {}, {
            r: index,
            s: common_vendor.o(($event) => handleSelect(item, index), index)
          });
        })
      } : {}, {
        c: !__props.list.length
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ebdf46e1"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/index/views/department/BasicEvaluation/components/UserList.vue"]]);
wx.createComponent(Component);
