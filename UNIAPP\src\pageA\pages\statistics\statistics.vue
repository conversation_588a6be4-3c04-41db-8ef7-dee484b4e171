<template>
   <view class="statistics delHead">

      <Head :title="title" returnType="back"></Head>
      <my-table :columns="columns" :data="tableData" />
   </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Head from '@/components/head.vue';
import MyTable from '@/components/myTable.vue';
import { getAppraisalStatistics } from '@/utils/http';
import { onLoad, onShow } from '@dcloudio/uni-app';
const title = ref("统计分")
const formData = ref({})
onLoad((e: any) => {
   // console.log(e);
   formData.value = e
   title.value = e.title
   delete formData.value.title
})
// 定义表格列配置
const columns = [
   {
      title: "自评总分",
      prop: "ZPScore",
      width: 1,
   },
   {
      title: "互评平均分",
      prop: "HPScorepjf",
      width: 1,
   },
   {
      title: "上级领导对下属评分",
      prop: "SPScore",
      width: 2,
   },
   {
      title: "下评平均分",
      prop: "XPScorepjf",
      width: 1,
   },
   {
      title: "综合总分",
      prop: "ZHScore",
      width: 1,
   }


];

// 定义表格数据的接口
interface TableDataType {
   ZPScore: string | number;
   HPScorepjf: string | number;
   SPScore: string | number;
   XPScorepjf: string | number;
   ZHScore: string | number;
}

// 表格数据
const tableData = ref<TableDataType[]>([
   // { id: 1, name: '张三', age: 28, job: '工程师
]);
const getASStics = async () => {
   const res = await getAppraisalStatistics(formData.value);
   const data = res.data.Data.listjson
   uni.hideLoading()
   tableData.value = [{ ...data }] as TableDataType[];
   console.log('表格数据:', tableData.value);
}





onShow(async () => {
   getASStics()

})
</script>
<style lang="scss" scoped>
.statistics {}
</style>
