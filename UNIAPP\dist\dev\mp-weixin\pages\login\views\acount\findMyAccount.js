"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "findMyAccount",
  setup(__props) {
    const step = common_vendor.ref(2);
    const numList = common_vendor.ref([{
      name: "上传证明资料"
    }, {
      name: "客服审核"
    }, {
      name: "完成"
    }]);
    let step_status = common_vendor.ref("");
    let check_result = common_vendor.ref("申诉成功");
    let check_text = common_vendor.ref("设置新密码登录");
    const returnSetp = () => {
      common_vendor.index.navigateBack({
        delta: 1
      });
    };
    const toUpload = () => {
      common_vendor.index.navigateTo({
        url: "/pages/login/views/acount/uploadIDCard"
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(returnSetp),
        b: common_vendor.f(numList.value, (item, index, i0) => {
          return {
            a: common_vendor.n(index == 2 ? common_vendor.unref(step_status) : ""),
            b: index + 1,
            c: common_vendor.t(item.name),
            d: common_vendor.n(index > step.value ? "" : "text-blue"),
            e: index
          };
        }),
        c: common_vendor.o(($event) => toUpload()),
        d: common_vendor.n(step.value === 0 ? "" : "none"),
        e: common_vendor.n(step.value === 1 ? "" : "none"),
        f: common_vendor.t(common_vendor.unref(check_result)),
        g: common_vendor.t(common_vendor.unref(check_text)),
        h: common_vendor.o(($event) => toUpload()),
        i: common_vendor.n(step.value === 2 ? "" : "none")
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-96f699ac"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/login/views/acount/findMyAccount.vue"]]);
wx.createPage(MiniProgramPage);
