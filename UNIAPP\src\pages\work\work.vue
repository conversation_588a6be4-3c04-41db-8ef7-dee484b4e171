<template>
  <view class="pageBox">
    <view class="header">
      <view class="companyBox">
        <image
          class="pic"
          src="../../static/images/logo.png"
          mode="aspectFit"></image>
        <image class="pic2" src="../../static/work/logo1.png" mode=""></image>
        <view class="name">深圳海凌科电子科技有限公司</view>
      </view>
    </view>
    <view class="main">
      <view
        class="item"
        v-for="(item, index) in list"
        :key="item"
        @click="toRouter(item.path)">
        <view style="flex: 1">
          <image class="icons" :src="item.icon" mode="aspectFit"></image>
        </view>
        <view style="flex: 4; justify-content: left">{{ item.name }}</view>
        <view style="flex: 1; text-align: right">
          <image
            style="margin-left: auto; height: 35rpx"
            src="../../static/work/back.png"
            mode="aspectFit"></image>
        </view>
      </view>
    </view>
    <toast ref="ref_toast"></toast>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import toast from "../../components/toast.vue";
import type { IToast } from "@/types";
import { getHistoryList } from "@/utils/http";
const ref_toast = ref<IToast>();
const list = ref([
  {
    icon: "../../static/work/01.png",
    name: "申请互评列表",
    path: "./../../pageA/pages/applicationList/applicationList",
  },
  {
    icon: "../../static/work/02.png",
    name: "历史评测",
    path: "./views/history",
  },
  // { icon: '../../static/images/avatar.png', name: '人事专用', path: '../../pageA/pages/hr/hr' },
  // { icon: '../../static/work/01.png', name: '评测考核' },
  // { icon: '../../static/work/02.png', name: '历史评测',path:'./views/history' },
  // { icon: '../../static/work/03.png', name: '企业管理' },
  // { icon: '../../static/work/04.png', name: '新成员申请' },
  // { icon: '../../static/work/05.png', name: '邀请好友' },
]);
const toRouter = (url: string) => {
  uni.navigateTo({
    url,
  });
};
</script>

<style scoped>
.header {
  padding-top: 14vh;
  background: #5266e2;
  position: relative;
}

.companyBox {
  position: absolute;
  top: 50%;
  left: 5%;
  margin: auto;
  width: 90%;
  height: 15vh;
  padding: 20rpx;
  background-color: white;
  display: flex;
  flex-direction: column;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(82, 102, 226, 0.1);
  border-radius: 10rpx;
  display: flex;
  justify-content: center;
  place-items: center;
}

.pic {
  position: absolute;
  top: 32%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 10rpx;
  width: 27vw;
  border-radius: 50%;
  filter: drop-shadow(0px 0 1px #07b3fc);
  /* border: 4px solid white;
		box-shadow: 0 0 4px white; */
}

.pic2 {
  position: absolute;
  top: 42%;
  left: 75%;
  transform: translate(-50%, -50%);
  width: 120rpx;
  height: 44rpx;
}

.name {
  margin-top: 55rpx;
  letter-spacing: 1px;
  font-size: 31rpx;
  color: #666666;
  padding: 40rpx;
  text-align: center;
  z-index: 100;
}

.main {
  margin-top: 10vh;
  width: 90%;
  padding: 10rpx 0rpx;
  border-radius: 10px;
  margin-left: 5%;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(82, 102, 226, 0.1);
}

.main > .item {
  width: 90%;
  height: 110rpx;
  padding: 10rpx 0rpx;
  /* margin: 25rpx auto; */
  border-bottom: 1px solid #f4f4f4;
  display: flex;
  color: var(--bk-3);
}

.item:last-child {
  border-bottom: none;
}

.item > view {
  width: 100%;
  height: 100%;
  line-height: 60rpx;
  font-size: var(--size-4);
  letter-spacing: 2rpx;
  display: flex;
  justify-content: center;
  place-items: center;
  /* border: 1px solid #ccc; */
}

.item > view > image {
  width: 38rpx;
  height: 74rpx;
  /* border: 1px solid gray; */
}

.icons {
  width: 15vw !important;
}
</style>
