
.pageBox.data-v-f60df41e {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}
.content.data-v-f60df41e {
  margin: auto;
  width: 92%;
  height: 75%;
  border-radius: 25rpx;
  z-index: 3;
  background-color: white;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(4, 0, 0, 0.12);
}
.content1.data-v-f60df41e {
  position: absolute;
  top: 12.7%;
  left: 5%;
  width: 90%;
  height: 75%;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(4, 0, 0, 0.1);
  border-radius: 25rpx;
  z-index: 2;
}
.content2.data-v-f60df41e {
  position: absolute;
  top: 11.7%;
  left: 6%;
  width: 88%;
  height: 75%;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(4, 0, 0, 0.08);
  border-radius: 25rpx;
  z-index: 1;
}
.contentHead.data-v-f60df41e {
  display: flex;
  flex-direction: column;
  justify-content: right;
  font-size: 30rpx;
  padding: 20rpx 10rpx 0rpx 10rpx;
  text-indent: 10px;
}
.contentHead > view.data-v-f60df41e:nth-child(n + 2) {
  padding: 5rpx 0rpx;
}
.contentTitle.data-v-f60df41e {
  width: 100%;
  text-indent: 30rpx;
  font-size: 34rpx;
  line-height: 70rpx;
  height: 80rpx;
  letter-spacing: 1rpx;
  position: relative;
}
.contentTitle > image.data-v-f60df41e {
  margin-left: auto;
  margin-right: 40rpx;
  height: 120rpx;
  width: 120rpx;
  border-radius: 10px;
}
.contentTitle.data-v-f60df41e::before {
  position: absolute;
  content: "";
  left: 5%;
  bottom: 5%;
  width: 60rpx;
  height: 10rpx;
  background: linear-gradient(to right, dodgerblue 5%, blue);
  box-shadow: 0 0 2px #ccc;
  border-radius: 10px;
}
.questionContainer.data-v-f60df41e {
  margin-top: 30rpx;
  width: 92%;
  margin-left: 4%;
  height: 66%;
  padding: 10rpx 15rpx;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 3;
}
.tx.data-v-f60df41e {
  position: absolute;
  margin-top: 35rpx;
  right: 70rpx;
  width: 20vw;
  height: 20vw;
  box-shadow: 1px 0 4px rgba(240, 240, 240, 1);
}
.questionBox.data-v-f60df41e {
  margin-top: 30rpx;
  width: 92%;
  margin-left: 4%;
  height: 66%;
  padding: 10rpx;
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 3;
}
.tips.data-v-f60df41e {
  margin-top: 30rpx;
  width: 100%;
  padding: 10rpx;
  border-radius: 10px;
  z-index: 3;
  color: #666666;
  white-space: nowrap;
}
.tips > view.data-v-f60df41e:nth-child(1) {
  font-size: 22rpx;
  text-align: center;
}
.tips > view.data-v-f60df41e:nth-child(2) {
  margin-top: 10rpx;
  font-size: 23rpx;
  text-align: center;
}
.textarea.data-v-f60df41e {
  width: 96%;
  margin-left: 2%;
  background-color: white;
  padding: 20rpx 20rpx 40rpx 20rpx;
  min-height: 50%;
  border-radius: 5px;
  border: none;
  color: #666666;
  letter-spacing: 1rpx;
}
.textarea.data-v-f60df41e:before {
  content: attr(data-currentCount);
  position: absolute;
  right: 106rpx;
  bottom: 5px;
  color: #ccc;
}
.textarea.data-v-f60df41e::after {
  content: attr(data-maxLength);
  position: absolute;
  right: 5px;
  bottom: 5px;
  color: #666666;
}
.bottom.data-v-f60df41e {
  margin-top: 70rpx;
  width: 100%;
  border-top: 1px solid rgba(240, 240, 240, 1);
  color: #666666;
}
.bottom > view.data-v-f60df41e {
  padding: 40rpx;
}
.submitText.data-v-f60df41e {
  margin-left: auto;
  width: 180rpx;
  font-size: 36rpx;
  text-align: center;
  background-color: #f7f7f7;
}
._input.data-v-f60df41e {
  padding: 10rpx;
  height: 70rpx;
  line-height: 70px;
  border-radius: 5px;
}
.maybe.data-v-f60df41e {
  color: #888888;
  font-size: 26rpx;
  letter-spacing: 1px;
}
.time.data-v-f60df41e {
  margin-right: 56rpx;
}
.score.data-v-f60df41e {
  font-weight: bold;
  color: black;
}
