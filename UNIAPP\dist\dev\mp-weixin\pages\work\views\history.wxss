
.head.data-v-284ecb32 {
  background: linear-gradient(81deg, #586ce2, #2330df);
  color: white;
}
.extendHead.data-v-284ecb32 {
  background: linear-gradient(81deg, #586ce2, #2330df);
  height: 140rpx;
  margin-top: -2px;
  padding: 0;
  position: relative;
}
.box.data-v-284ecb32 {
  position: absolute;
  top: 240rpx;
  width: 90%;
  min-height: 85vh;
  margin-left: 5%;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(82, 102, 226, 0.1);
  overflow: auto !important;
}
.yearBox.data-v-284ecb32 {
  position: absolute;
  top: 200rpx;
  left: 5vw;
  min-width: 260rpx;
  z-index: 100;
  padding: 15rpx 10rpx 14rpx 15rpx;
  background-color: white;
  border-radius: 10px 10px 0px 0px;
  color: #666666;
  display: flex;
  /* border: 1px solid #000; */
}
.year.data-v-284ecb32 {
  border-bottom: 1px solid #02d7ee;
  letter-spacing: 1px;
  color: dodgerblue;
}
.cu-item.data-v-284ecb32 {
  width: 100% !important;
  padding: 40rpx;
  font-size: 30rpx;
  border-top: 1px solid #eef1fe;
}
.tag.data-v-284ecb32 {
  font-size: 22rpx;
  margin-left: auto;
  /* margin-left: 20rpx; */
  margin-right: 120rpx;
  padding: 0rpx 0rpx 5rpx 5rpx;
  width: 2px;
  height: 30rpx;
  box-shadow: 0 0 4px #ccc;
  border-radius: 5px;
  background-color: dodgerblue;
  color: #666666;
  white-space: nowrap;
  text-indent: 10rpx;
}
.look.data-v-284ecb32 {
  margin-left: auto;
  margin-right: 15rpx;
  color: #2979ff;
  font-size: 14px;
}
