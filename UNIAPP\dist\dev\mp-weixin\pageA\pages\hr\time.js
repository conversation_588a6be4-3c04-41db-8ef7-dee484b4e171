"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_http = require("../../../utils/http.js");
require("../../../utils/request.js");
require("../../../utils/index.js");
require("../../../store/index.js");
require("../../../store/user.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
if (!Array) {
  const _easycom_up_input2 = common_vendor.resolveComponent("up-input");
  const _easycom_up_form_item2 = common_vendor.resolveComponent("up-form-item");
  const _easycom_up_col2 = common_vendor.resolveComponent("up-col");
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  const _easycom_up_row2 = common_vendor.resolveComponent("up-row");
  const _easycom_up_form2 = common_vendor.resolveComponent("up-form");
  (_easycom_up_input2 + _easycom_up_form_item2 + _easycom_up_col2 + _easycom_up_button2 + _easycom_up_row2 + _easycom_up_form2)();
}
const _easycom_up_input = () => "../../../node-modules/uview-plus/components/u-input/u-input.js";
const _easycom_up_form_item = () => "../../../node-modules/uview-plus/components/u-form-item/u-form-item.js";
const _easycom_up_col = () => "../../../node-modules/uview-plus/components/u-col/u-col.js";
const _easycom_up_button = () => "../../../node-modules/uview-plus/components/u-button/u-button.js";
const _easycom_up_row = () => "../../../node-modules/uview-plus/components/u-row/u-row.js";
const _easycom_up_form = () => "../../../node-modules/uview-plus/components/u-form/u-form.js";
if (!Math) {
  (Head + _easycom_up_input + _easycom_up_form_item + _easycom_up_col + _easycom_up_button + _easycom_up_row + _easycom_up_form + toast)();
}
const Head = () => "../../../components/head.js";
const toast = () => "../../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "time",
  setup(__props) {
    const ref_toast = common_vendor.ref();
    const formRef = common_vendor.ref("");
    const state = common_vendor.reactive({
      formModel: {
        formData: {
          SubmitDay: "",
          //提出跨部门打分申请的开始日期5
          AssignDay: "",
          //主管审核/指定跨部门打分的截止日期8
          UrgedDay: "",
          //人事催促打分截止日期10
          CompleteDay: ""
          //停止打分日期12
        }
      },
      rules: {
        "formData.SubmitDay": {
          type: "integer",
          required: true,
          message: "请输入跨部门打分的开始日期",
          trigger: ["blur"],
          asyncValidator: (rule, value, callback) => {
            return onAsyncValidator(rule, value, callback);
          }
        },
        "formData.AssignDay": {
          type: "integer",
          required: true,
          message: "请输入跨部门打分的截止日期",
          trigger: ["blur"],
          asyncValidator: (rule, value, callback) => {
            return onAsyncValidator(rule, value, callback);
          }
        },
        "formData.UrgedDay": {
          type: "integer",
          required: true,
          message: "请输入催促打分日期",
          trigger: ["blur"],
          asyncValidator: (rule, value, callback) => {
            return onAsyncValidator(rule, value, callback);
          }
        },
        "formData.CompleteDay": {
          type: "integer",
          required: true,
          message: "请输入停止打分日期",
          trigger: ["blur"],
          asyncValidator: (rule, value, callback) => {
            return onAsyncValidator(rule, value, callback);
          }
        }
      }
    });
    common_vendor.onLoad(() => {
      getDeadlineAPI();
    });
    const onAsyncValidator = (rule, value, callback) => {
      var _a, _b, _c, _d, _e, _f, _g;
      const num = parseInt(value);
      if (isNaN(num) || num <= 0 || num > 15 || num !== Number(value)) {
        (_a = ref_toast.value) == null ? void 0 : _a.warning("日期可选每月的1-15号");
        callback(new Error());
        return;
      }
      const formData = state.formModel.formData;
      const fieldName = rule.field;
      if (fieldName === "SubmitDay" && parseInt(formData.AssignDay) && parseInt(formData.SubmitDay) >= parseInt(formData.AssignDay)) {
        (_b = ref_toast.value) == null ? void 0 : _b.warning("开始日期必须小于截止日期");
        callback(new Error());
        return;
      }
      if (fieldName === "AssignDay") {
        if (parseInt(formData.SubmitDay) && parseInt(formData.AssignDay) <= parseInt(formData.SubmitDay)) {
          (_c = ref_toast.value) == null ? void 0 : _c.warning("截止日期必须大于开始日期");
          callback(new Error());
          return;
        }
        if (parseInt(formData.UrgedDay) && parseInt(formData.AssignDay) >= parseInt(formData.UrgedDay)) {
          (_d = ref_toast.value) == null ? void 0 : _d.warning("截止日期必须小于催促日期");
          callback(new Error());
          return;
        }
      }
      if (fieldName === "UrgedDay") {
        if (parseInt(formData.AssignDay) && parseInt(formData.UrgedDay) <= parseInt(formData.AssignDay)) {
          (_e = ref_toast.value) == null ? void 0 : _e.warning("催促日期必须大于截止日期");
          callback(new Error());
          return;
        }
        if (parseInt(formData.CompleteDay) && parseInt(formData.UrgedDay) >= parseInt(formData.CompleteDay)) {
          (_f = ref_toast.value) == null ? void 0 : _f.warning("催促日期必须小于停止日期");
          callback(new Error());
          return;
        }
      }
      if (fieldName === "CompleteDay" && parseInt(formData.UrgedDay) && parseInt(formData.CompleteDay) <= parseInt(formData.UrgedDay)) {
        (_g = ref_toast.value) == null ? void 0 : _g.warning("停止日期必须大于催促日期");
        callback(new Error());
        return;
      }
      callback();
    };
    const onSubmit = async () => {
      var _a, _b;
      const valid = await ((_a = formRef.value) == null ? void 0 : _a.validate());
      if (valid) {
        getUpDeadline();
      } else {
        (_b = ref_toast.value) == null ? void 0 : _b.warning("请先完善表单！");
      }
    };
    const getDeadlineAPI = async () => {
      const res = await utils_http.getDeadline();
      const data = res.data.Data;
      state.formModel.formData = data;
    };
    const getUpDeadline = async () => {
      var _a, _b;
      let res = await utils_http.getUpdateDeadline(state.formModel.formData);
      if (res.data.Code != 1) {
        (_a = ref_toast.value) == null ? void 0 : _a.info(res.data.Message);
      } else {
        (_b = ref_toast.value) == null ? void 0 : _b.success(res.data.Message);
        const data = res.data.Data;
        state.formModel.formData = data;
      }
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          title: "评分日期",
          returnType: "mine"
        }),
        b: common_vendor.t("<"),
        c: common_vendor.t("<"),
        d: common_vendor.t("<"),
        e: common_vendor.o(($event) => state.formModel.formData.SubmitDay = $event),
        f: common_vendor.p({
          type: "number",
          clearable: true,
          placeholder: "请输入内容",
          modelValue: state.formModel.formData.SubmitDay
        }),
        g: common_vendor.p({
          label: "跨部门打分的开始日期:",
          prop: "formData.SubmitDay"
        }),
        h: common_vendor.o(($event) => state.formModel.formData.AssignDay = $event),
        i: common_vendor.p({
          type: "number",
          clearable: true,
          placeholder: "请输入内容",
          modelValue: state.formModel.formData.AssignDay
        }),
        j: common_vendor.p({
          label: "跨部门打分的截止日期:",
          prop: "formData.AssignDay"
        }),
        k: common_vendor.o(($event) => state.formModel.formData.UrgedDay = $event),
        l: common_vendor.p({
          type: "number",
          clearable: true,
          placeholder: "请输入内容",
          modelValue: state.formModel.formData.UrgedDay
        }),
        m: common_vendor.p({
          label: "催促打分日期:",
          prop: "formData.UrgedDay"
        }),
        n: common_vendor.o(($event) => state.formModel.formData.CompleteDay = $event),
        o: common_vendor.p({
          type: "number",
          clearable: true,
          placeholder: "请输入内容",
          modelValue: state.formModel.formData.CompleteDay
        }),
        p: common_vendor.p({
          label: "停止打分日期:",
          prop: "formData.CompleteDay"
        }),
        q: common_vendor.p({
          span: "3"
        }),
        r: common_vendor.o(onSubmit),
        s: common_vendor.p({
          type: "primary",
          text: "提交"
        }),
        t: common_vendor.p({
          span: "6"
        }),
        v: common_vendor.p({
          customStyle: "margin: 10px"
        }),
        w: common_vendor.sr(formRef, "1a5e0555-1", {
          "k": "formRef"
        }),
        x: common_vendor.p({
          labelPosition: "top",
          model: state.formModel,
          rules: state.rules
        }),
        y: common_vendor.sr(ref_toast, "1a5e0555-14", {
          "k": "ref_toast"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1a5e0555"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pageA/pages/hr/time.vue"]]);
wx.createPage(MiniProgramPage);
