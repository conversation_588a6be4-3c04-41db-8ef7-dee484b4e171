<template>
  <view class="user-list">
    <up-collapse v-if="list.length">
      <view
        class="cu-chat"
        v-for="(item, index) in list"
        :key="index"
        data-selected="false"
        @click="handleSelect(item, index)">
        <view class="cu-item" v-if="item.ID != 1">
          <view class="picBox">
            <image
              class="pic"
              :src="
                item.Avatar
                  ? http + item.Avatar
                  : '../../../../../../static/images/avatar.png'
              "
              alt="" />
          </view>
          <view class="row">
            <view :class="{ 'row-left': TIdNum > 3 }">
              <view class="nameBox">
                {{ item.DisplayName || item.FullPathName || item.Name }}
                <view class="tagW">
                  <up-tag
                    v-if="item.IsManager"
                    text="主管"
                    plain
                    size="mini"
                    type="warning" />
                </view>
              </view>
              <view class="department">{{ item.Ex4 }}</view>
            </view>
            <view class="row-right" v-if="TIdNum > 3">
              <up-tag
                @click.stop="$emit('selectSubordinate', item)"
                v-if="
                  TIdNum > 4
                    ? item.DisplayName && !selectedSubordinate
                    : item.DisplayName
                "
                text="被评人"
                type="error" />
              <up-tag
                @click.stop="$emit('selectEvaluator', item)"
                v-if="item.DisplayName && TIdNum > 4 && selectedSubordinate"
                text="评测人"
                type="success" />
            </view>
          </view>
          <!-- <view class="department">{{ item.Ex4 }}</view> -->
        </view>
      </view>
    </up-collapse>
    <view
      class="noData"
      v-show="!list.length && !(TIdNum === 5 && selectedSubordinate)">
      <view class="" style="width: 90%; margin-left: 5%">
        <image
          src="../../../../../../static/index/noData.png"
          mode="aspectFit"></image>
      </view>
      <view
        class=""
        style="
          width: 100%;
          text-align: center;
          margin-top: 20rpx;
          color: dodgerblue;
        ">
        无数据哦
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { getBaseUrl } from "@/utils/request";
const http = getBaseUrl().replace(/\Api\/V1$/, "");
// 在文件顶部添加
type PropType<T> = import("vue").PropType<T>;
interface ListItem {
  DisplayName?: string;
  FullPathName?: string;
  Name?: string;
  IsManager?: boolean;
  Ex4?: string;
  ID?: number;
  Id?: number | string;
  Avatar?: string;
}
defineProps({
  list: {
    type: Array as PropType<ListItem[]>,
    default: () => [],
  },
  TIdNum: {
    type: Number,
    required: true,
  },
  selectedSubordinate: {
    type: String,
    default: "",
  },
  searchValue: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["select", "selectSubordinate", "selectEvaluator"]);

const handleSelect = (item: any, index: number) => {
  emit("select", item, index);
};
</script>

<style lang="scss" scoped>
.user-list {
  width: 100%;
}

.cu-chat[data-selected="true"] {
  background-color: red;
}

.cu-item {
  padding: 10rpx;
  border-bottom: 1px solid #eef1fe;
  display: flex;
}

.row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #666666;
  font-size: 28rpx;
  flex: 1;

  .row-left {
    width: 320rpx;
  }

  .row-right {
    width: 240rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  .nameBox {
    display: flex;
    align-items: center;
  }
  .tagW {
    // width: 120rpx !important;
    margin-top: 4px;
    margin-left: 4px;
  }

  :deep(.u-tag) {
    margin-top: 10rpx !important;
    margin-right: 10rpx !important;
  }
}

.picBox {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pic {
  border-radius: 50%;
  width: 40px;
  height: 40px !important;
  display: flex;
  margin-right: 14rpx;
}

.department {
  color: #8799a3;
  margin-top: 10rpx;
  font-size: 22rpx;
}

.noData {
  width: 100%;
  margin-top: 20rpx;
}
</style>
