"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../utils/request.js");
require("../../store/index.js");
require("../../utils/index.js");
require("../../store/user.js");
require("../../store/pinia.js");
require("../../utils/http.js");
require("../../utils/sign.js");
require("../../utils/sha1.js");
if (!Math) {
  toast();
}
const toast = () => "../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "work",
  setup(__props) {
    const ref_toast = common_vendor.ref();
    const list = common_vendor.ref([
      {
        icon: "../../static/work/01.png",
        name: "申请互评列表",
        path: "./../../pageA/pages/applicationList/applicationList"
      },
      {
        icon: "../../static/work/02.png",
        name: "历史评测",
        path: "./views/history"
      }
      // { icon: '../../static/images/avatar.png', name: '人事专用', path: '../../pageA/pages/hr/hr' },
      // { icon: '../../static/work/01.png', name: '评测考核' },
      // { icon: '../../static/work/02.png', name: '历史评测',path:'./views/history' },
      // { icon: '../../static/work/03.png', name: '企业管理' },
      // { icon: '../../static/work/04.png', name: '新成员申请' },
      // { icon: '../../static/work/05.png', name: '邀请好友' },
    ]);
    const toRouter = (url) => {
      common_vendor.index.navigateTo({
        url
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(list.value, (item, index, i0) => {
          return {
            a: item.icon,
            b: common_vendor.t(item.name),
            c: item,
            d: common_vendor.o(($event) => toRouter(item.path), item)
          };
        }),
        b: common_vendor.sr(ref_toast, "1297a68e-0", {
          "k": "ref_toast"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1297a68e"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/work/work.vue"]]);
wx.createPage(MiniProgramPage);
