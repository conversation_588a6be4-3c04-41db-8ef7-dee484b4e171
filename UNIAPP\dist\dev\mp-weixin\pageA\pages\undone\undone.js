"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_up_collapse_item2 = common_vendor.resolveComponent("up-collapse-item");
  const _easycom_up_collapse2 = common_vendor.resolveComponent("up-collapse");
  (_easycom_up_collapse_item2 + _easycom_up_collapse2)();
}
const _easycom_up_collapse_item = () => "../../../node-modules/uview-plus/components/u-collapse-item/u-collapse-item.js";
const _easycom_up_collapse = () => "../../../node-modules/uview-plus/components/u-collapse/u-collapse.js";
if (!Math) {
  (Head + _easycom_up_collapse_item + _easycom_up_collapse)();
}
const Head = () => "../../../components/head.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "undone",
  setup(__props) {
    const data = common_vendor.ref([]);
    common_vendor.onLoad((e) => {
      if (e.data) {
        data.value = JSON.parse(decodeURIComponent(e.data));
      }
    });
    const onTitle = (t) => {
      const Id = data.value.id;
      if (Id == 0) {
        return t.DisplayName;
      }
      if (Id == 1) {
        return t.UserName;
      }
      if (Id == 2) {
        return t.SupervisorName;
      }
      if (Id == 3) {
        return t.SubordinateName;
      }
    };
    return (_ctx, _cache) => {
      var _a;
      return {
        a: common_vendor.p({
          title: data.value.title,
          returnType: "home"
        }),
        b: common_vendor.f(data.value.originList, (t, i, i0) => {
          var _a2, _b, _c, _d;
          return common_vendor.e({
            a: t.NotEvaluatedUsersCount || ((_a2 = t.NotEvaluatedSubordinates) == null ? void 0 : _a2.length)
          }, t.NotEvaluatedUsersCount || ((_b = t.NotEvaluatedSubordinates) == null ? void 0 : _b.length) ? {
            b: common_vendor.t(t.NotEvaluatedUsersCount || t.NotEvaluatedSubordinates.length || "")
          } : {}, {
            c: (_c = t.NotEvaluatedSubordinates) == null ? void 0 : _c.length
          }, ((_d = t.NotEvaluatedSubordinates) == null ? void 0 : _d.length) ? {
            d: common_vendor.f(t.NotEvaluatedSubordinates, (v, k, i1) => {
              return {
                a: common_vendor.t(v.DepartmentName),
                b: common_vendor.t(v.DisplayName),
                c: k
              };
            })
          } : {}, {
            e: i,
            f: "55fb016d-2-" + i0 + ",55fb016d-1",
            g: common_vendor.p({
              disabled: data.value.id != 2,
              title: onTitle(t)
            })
          });
        }),
        c: common_vendor.p({
          accordion: true
        }),
        d: ((_a = data.value.textList) == null ? void 0 : _a.length) > 14 ? 1 : ""
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-55fb016d"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pageA/pages/undone/undone.vue"]]);
wx.createPage(MiniProgramPage);
