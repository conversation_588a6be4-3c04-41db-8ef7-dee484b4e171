<view class="pageBox data-v-5468c562"><view class="head data-v-5468c562"><view class="return data-v-5468c562" bindtap="{{a}}"><image class="return-img data-v-5468c562" src="/static/icons/fanhui.png" mode="aspectFit"></image></view><view class="title data-v-5468c562">个人信息</view></view><view class="main data-v-5468c562"><view wx:for="{{b}}" wx:for-item="item" wx:key="p" class="content data-v-5468c562"><view class="label data-v-5468c562">{{item.a}}</view><view class="value data-v-5468c562"><button wx:if="{{item.b}}" class="specialBtn data-v-5468c562" open-type="chooseAvatar" bindchooseavatar="{{item.d}}"><image class="avatar data-v-5468c562" src="{{item.c}}" mode="aspectFit"></image></button><input wx:if="{{item.e}}" class="input data-v-5468c562" type="text" value="{{item.f}}" bindinput="{{item.g}}"/><text wx:if="{{item.h}}" class="data-v-5468c562" bindtap="{{item.j}}">{{item.i}}</text><view wx:if="{{item.k}}" class="flex data-v-5468c562" style="letter-spacing:6rpx;place-items:center" bindtap="{{item.l}}"><view class=" data-v-5468c562">******</view></view><view wx:if="{{item.m}}" class=" data-v-5468c562" bindtap="{{item.o}}">{{item.n}}</view></view></view></view><view class="data-v-5468c562" style="width:60%;margin:30rpx auto"><up-button wx:if="{{d}}" class="data-v-5468c562" u-s="{{['d']}}" bindclick="{{c}}" u-i="5468c562-0" bind:__l="__l" u-p="{{d}}">保存</up-button></view><toast class="r data-v-5468c562" u-r="ref_toast" u-i="5468c562-1" bind:__l="__l"></toast></view>