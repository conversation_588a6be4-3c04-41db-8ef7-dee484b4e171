<template>
  <view class="pageBox">
    <view class="header">
      <image
        class="head_bg"
        src="../../static/mine/head_bg1.png"
        mode="aspectFill"></image>

      <view class="avatarBox" @click="toUserInfo()">
        <view class="avatar">
          <image
            :src="
              cantBeBad(userStore.userInfo.Avatar) ||
              '../../static/images/avatar.png'
            "
            mode="aspectFit"></image>
        </view>
      </view>
      <view
        class="username"
        style="font-size: var(--size-2); text-indent: 30rpx"
        @click="toUserInfo()">
        {{ userStore.userInfo.DisplayName }}
        <image
          src="../../static/mine/edit.png"
          mode="aspectFit"
          style="width: 20rpx; height: 20px; margin-left: 10rpx"></image>
      </view>
      <view class="position" style="color: #888888; font-size: var(--size-1)">
        - {{ userStore.userInfo.Ex4 }}
      </view>
    </view>
    <view class="main">
      <view
        class="item"
        v-for="(item, index) in list"
        :key="item.id"
        @click="toRouter(item.path)">
        <view style="flex: 1">
          <image
            :src="item.icon"
            style="width: 60rpx; height: 100%"
            mode="aspectFit"></image>
        </view>
        <view style="flex: 4; justify-content: left">{{ item.name }}</view>
        <view style="flex: 1; text-align: right">
          <image
            style="margin-left: 50rpx"
            src="../../static/mine/right.png"
            mode="aspectFit"></image>
        </view>
      </view>
    </view>
    <toast ref="ref_toast"></toast>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useUserStore } from "@/store/user";
import { cantBeBad } from "@/utils";
import { onShow } from "@dcloudio/uni-app";
import { getUserInfo } from "@/utils/http";
import toast from "@/components/toast.vue";
import type { IToast } from "@/types";
const ref_toast = ref<IToast>();
const userStore = useUserStore();
const list = ref([
  {
    id: 3,
    icon: "../../static/mine/renshi.png",
    name: "人事专用",
    path: "../../pageA/pages/hr/hr",
  },
  // {
  //   id: 4,
  //   icon: "../../static/mine/riqi.png",
  //   name: "评分日期",
  //   path: "../../pageA/pages/hr/time",
  // },
  {
    id: 2,
    icon: "../../static/mine/icon3.png",
    name: "设置",
    path: "./views/setting",
  },
  // { id: 0, icon: '../../static/work/02.png', name: '历史评测', path: '../work/views/history' },
  // { id: 1, icon: '../../static/mine/icon1.png', name: '收到评价', path: './views/receiveComments' },
]);

const toRouter = (url: string) => {
  uni.navigateTo({
    url,
  });
};
/** 获取用户信息 */
const getUserInfoAPI = async () => {
  const res = await getUserInfo();
  const data = res.data.Data;
  if (data) {
    userStore.userInfo = data;
    console.log("用户信息", data);
    return;
  } else {
    ref_toast.value?.fail(res.data.Message);
  }
};
const toUserInfo = () => uni.navigateTo({ url: "../mine/views/userInfo" });
onShow(() => {
  getUserInfoAPI();
});
</script>

<style scoped>
.header {
  width: 100%;
  height: 536rpx;
  border: 1px solid transparent;
  /* background-image: url('../../static/mine/head_bg.png');
		background-size: 100% 100%;
		background-repeat: no-repeat;
		background-position: -90rpx -10rpx; */
  position: relative;
}

.head_bg {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  /* border: 1px solid ; */
}

.header > view {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 50rpx;
  letter-spacing: 2rpx;
  text-align: center;
  display: flex;
  place-content: center;
  /* border: 1px solid ; */
}

.edit {
  position: absolute;
  top: 50%;
  width: 92% !important;
  text-align: left !important;
  text-indent: 20px;
  /* border: 1px solid red; */
}

.avatarBox {
  margin-top: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100% !important;
  height: 185rpx !important;
}

.avatar {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #ccc;
  box-shadow: 0 0 4px #8b95ed;
  border: 1px solid skyblue;
}

.avatar > image {
  width: 100%;
  height: 100%;
}

.main {
  width: 100%;
  height: 800rpx;
  /* border: 1px solid ; */
}

.main > .item {
  width: 90%;
  height: 80rpx;
  padding: 10rpx;
  margin: 25rpx auto;
  /* border: 1px solid gray; */
  display: flex;
  color: var(--bk-3);
}

.item > view {
  width: 100%;
  height: 100%;
  line-height: 60rpx;
  font-size: var(--size-4);
  letter-spacing: 2rpx;
  display: flex;
  justify-content: center;
  place-items: center;
  /* border: 1px solid #ccc; */
}

.item > view > image {
  width: 38rpx;
  height: 44rpx;
  /* border: 1px solid gray; */
}
</style>
