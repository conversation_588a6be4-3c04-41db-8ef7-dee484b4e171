"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_http = require("../../../utils/http.js");
const store_user = require("../../../store/user.js");
require("../../../utils/request.js");
require("../../../utils/index.js");
require("../../../store/index.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
if (!Array) {
  const _easycom_up_loading_icon2 = common_vendor.resolveComponent("up-loading-icon");
  const _easycom_up_tag2 = common_vendor.resolveComponent("up-tag");
  const _easycom_up_textarea2 = common_vendor.resolveComponent("up-textarea");
  const _easycom_up_modal2 = common_vendor.resolveComponent("up-modal");
  (_easycom_up_loading_icon2 + _easycom_up_tag2 + _easycom_up_textarea2 + _easycom_up_modal2)();
}
const _easycom_up_loading_icon = () => "../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js";
const _easycom_up_tag = () => "../../../node-modules/uview-plus/components/u-tag/u-tag.js";
const _easycom_up_textarea = () => "../../../node-modules/uview-plus/components/u-textarea/u-textarea.js";
const _easycom_up_modal = () => "../../../node-modules/uview-plus/components/u-modal/u-modal.js";
if (!Math) {
  (Head + _easycom_up_loading_icon + _easycom_up_tag + toast + _easycom_up_textarea + _easycom_up_modal)();
}
const toast = () => "../../../components/toast.js";
const Head = () => "../../../components/head.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "applicationList",
  setup(__props) {
    const ref_toast = common_vendor.ref();
    const userStore = store_user.useUserStore();
    const loading = common_vendor.ref(true);
    const applicationList = common_vendor.ref([]);
    const showModal = common_vendor.ref(false);
    const admin = userStore.IsDepartmentlanager;
    const state = common_vendor.reactive({
      formData: {
        RequestId: "",
        Status: 0,
        Remark: "",
        Lng: ""
      }
    });
    const onConfirm = () => {
      showModal.value = false;
      state.formData.Status = 1;
      onSubmit();
    };
    const onCancel = () => {
      showModal.value = false;
      state.formData.Status = 2;
      onSubmit();
    };
    const onSubmit = async () => {
      var _a, _b, _c;
      if (!state.formData.Remark) {
        (_a = ref_toast.value) == null ? void 0 : _a.warning("请输入审批备注");
        return;
      }
      const res = await utils_http.getApplyforListRequest(state.formData);
      common_vendor.index.hideLoading();
      if (res.data.Code != 1) {
        (_b = ref_toast.value) == null ? void 0 : _b.info(res.data.Message);
      } else {
        (_c = ref_toast.value) == null ? void 0 : _c.success(res.data.Message);
        onReset();
      }
    };
    const onReset = () => {
      state.formData = {
        RequestId: "",
        Status: 0,
        Remark: "",
        Lng: ""
      };
      fetchApplicationList();
    };
    const onClose = () => {
      showModal.value = false;
    };
    const onApprovalPending = (e) => {
      if (e.Status == 1 || !admin)
        return;
      showModal.value = true;
      state.formData.RequestId = e.Id;
    };
    const fetchApplicationList = async () => {
      loading.value = true;
      try {
        let res;
        if (admin) {
          res = await utils_http.getSupervisorList();
        } else {
          res = await utils_http.getApplyforList();
        }
        common_vendor.index.hideLoading();
        if (res && res.data && res.data.Data && res.data.Data.listjson) {
          applicationList.value = res.data.Data.listjson;
        } else {
          applicationList.value = [];
        }
        common_vendor.index.hideLoading();
      } catch (error) {
        console.error("获取申请列表失败：", error);
        applicationList.value = [];
      } finally {
        loading.value = false;
      }
    };
    const getStatusText = (status) => {
      switch (status) {
        case 0:
          return "待审批";
        case 1:
          return "已通过";
        case 2:
          return "已拒绝";
        default:
          return "未知状态";
      }
    };
    const getStatusType = (status) => {
      switch (status) {
        case 0:
          return "info";
        case 1:
          return "success";
        case 2:
          return "error";
        default:
          return "info";
      }
    };
    common_vendor.onShow(async () => {
      setTimeout(async () => {
        await fetchApplicationList();
      }, 300);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "申请列表",
          returnType: "work"
        }),
        b: loading.value
      }, loading.value ? {
        c: common_vendor.p({
          size: "3vh"
        })
      } : applicationList.value.length === 0 ? {} : {
        e: common_vendor.f(applicationList.value, (item, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.ApplicantName),
            b: common_vendor.t(item.TargetDepartmentName),
            c: "bf6f1753-2-" + i0,
            d: common_vendor.p({
              text: getStatusText(item.Status),
              type: getStatusType(item.Status),
              size: "mini",
              plain: true
            }),
            e: common_vendor.t(item.TargetUserName),
            f: common_vendor.t(item.ApplyTimeStr),
            g: item.Status !== 0
          }, item.Status !== 0 ? {
            h: common_vendor.t(item.ApproveTimeStr !== "0001-01-01 00:00" ? item.ApproveTimeStr : "未审批")
          } : {}, {
            i: item.ApproverName
          }, item.ApproverName ? {
            j: common_vendor.t(item.ApproverName)
          } : {}, {
            k: common_vendor.t(item.Reason),
            l: item.Remark
          }, item.Remark ? {
            m: common_vendor.t(item.Remark)
          } : {}, {
            n: item.Id,
            o: common_vendor.o(($event) => onApprovalPending(item), item.Id)
          });
        })
      }, {
        d: applicationList.value.length === 0,
        f: common_vendor.sr(ref_toast, "bf6f1753-3", {
          "k": "ref_toast"
        }),
        g: common_vendor.o(($event) => state.formData.Remark = $event),
        h: common_vendor.p({
          ["cursor-spacing"]: "100",
          placeholder: "请输入审批备注",
          modelValue: state.formData.Remark
        }),
        i: common_vendor.o(onConfirm),
        j: common_vendor.o(onCancel),
        k: common_vendor.o(onClose),
        l: common_vendor.p({
          show: showModal.value,
          title: "审批备注",
          closeOnClickOverlay: true,
          showConfirmButton: "true",
          ["show-cancel-button"]: true,
          confirmText: "同意",
          cancelText: "拒绝"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-bf6f1753"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pageA/pages/applicationList/applicationList.vue"]]);
wx.createPage(MiniProgramPage);
