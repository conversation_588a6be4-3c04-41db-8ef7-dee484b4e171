/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.my-head.data-v-e012c461 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 11vh;
  display: flex;
  align-items: center;
  padding-top: 20rpx;
  background-color: #fff;
  z-index: 100;
  border-bottom: 1px solid #eee;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}
.my-head .my-title.data-v-e012c461 {
  position: absolute;
  width: 100%;
  left: 0;
  text-align: center;
  font-size: 34rpx;
  color: rgb(80, 80, 80);
  font-weight: 600;
}
.my-head .my-return.data-v-e012c461 {
  position: absolute;
  left: 0;
  padding: 20rpx 60rpx 0rpx 40rpx;
  z-index: 101;
  /* 确保返回按钮在最上层 */
}
.my-head .my-return-img.data-v-e012c461 {
  width: 24rpx;
  height: 40rpx;
}