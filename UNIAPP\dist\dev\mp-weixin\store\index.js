"use strict";
const common_vendor = require("../common/vendor.js");
const useIndexStore = common_vendor.defineStore(
  "index",
  {
    // state是一个函数，返回一个对象
    state: () => {
      return {
        isWx: true,
        // 选择部门人员后得到的被评人信息
        routerUserinfo: common_vendor.reactive({
          ID: 1,
          DisplayName: "未知用户",
          Sex: 1,
          Ex4: "销售部",
          Avatar: ""
        }),
        //提交页面的所需数据
        submitData: common_vendor.reactive({
          TId: 0,
          BId: 0,
          Content: [],
          Score: "0",
          Reamrk: "",
          ExtraScore: ""
        }),
        backAPage: () => {
          common_vendor.index.navigateBack({
            delta: 1
          });
        },
        resetData: function() {
          this.submitData = {
            TId: 0,
            BId: 0,
            Content: [],
            Score: "0",
            Reamrk: "",
            ExtraScore: ""
          };
        }
      };
    },
    getters: {},
    actions: {}
  }
);
exports.useIndexStore = useIndexStore;
