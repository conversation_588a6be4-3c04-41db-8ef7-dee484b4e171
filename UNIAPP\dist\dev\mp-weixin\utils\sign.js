"use strict";
const utils_sha1 = require("./sha1.js");
function jsonVAL(json) {
  var v = "";
  for (var i in json) {
    v += json[i];
  }
  return v;
}
function getTimestamp() {
  return Date.parse(/* @__PURE__ */ new Date());
}
function mtRand(min, max) {
  var result = Math.random() * (max - min + 1) + min;
  return parseInt(result);
}
function sign(json, server_token) {
  json.timestamp = getTimestamp();
  json.nonce = mtRand(1e5, 999999);
  json.appkey = server_token;
  var s = [json.timestamp.toString(), json.nonce.toString(), json.appkey];
  s.sort();
  json.signature = utils_sha1.sha1(jsonVAL(s));
  delete json.appkey;
  return json;
}
exports.sign = sign;
