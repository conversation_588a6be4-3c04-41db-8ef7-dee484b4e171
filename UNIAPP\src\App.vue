<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
// import { onMounted, provide, ref } from "vue";
onLaunch(() => {
	// console.log("App Launch");
});
onShow(() => {
	// console.log("App Show");
});
onHide(() => {
	// console.log("App Hide");
});
</script>
<!-- 公共样式 -->
<style lang="scss">
/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
@import "../node_modules/uview-plus/index.scss";
@import "../colorui/main.css";
@import "../colorui/icon.css";

@import './resat.scss';
</style>
