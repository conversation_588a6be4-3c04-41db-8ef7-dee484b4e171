/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.cu-dialog {
  width: -moz-fit-content;
  width: fit-content;
  padding: 0rpx 28rpx 0rpx 0rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  letter-spacing: 2rpx;
}
.cu-modal .cu-dialog > .cu-bar:first-child .action {
  min-height: 2.4rem !important;
}
.cu-bar {
  min-height: 2.8rem !important;
}
.cu-dialog {
  padding: 0px 45rpx 0px 15rpx;
}
.info {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
}
.success {
  background-color: #d7f0db;
  color: #39b54a;
}
.warning {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
}
.fail {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
}