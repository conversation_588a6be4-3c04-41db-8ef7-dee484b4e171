<template>
  <view class="my-head">
    <view class="my-return" @click="onReturn">
      <image
        class="my-return-img"
        src="../../../static/icons/fanhui.png"
        mode="aspectFit"></image>
    </view>
    <text class="my-title">{{ props.title }}</text>
  </view>
</template>
<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: "选择部门",
  },
  // 返回类型：'home'返回首页，'back'返回上一级
  returnType: {
    type: String,
    default: "home",
  },
  delta: {
    type: Number,
    default: 1,
  },
});

const onReturn = () => {
  // console.log("返回类型:", props.returnType);
  if (props.returnType === "back") {
    // 返回上一级
    uni.navigateBack({
      delta: props.delta,
    });
  } else if (props.returnType === "work") {
    uni.switchTab({
      url: "/pages/work/work",
    });
  } else if (props.returnType === "mine") {
    uni.switchTab({
      url: "/pages/mine/mine",
    });
  } else {
    // 返回首页
    uni.switchTab({
      url: "/pages/index/index",
    });
  }
};
</script>
<style lang="scss" scoped>
.my-head {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 11vh;
  display: flex;
  align-items: center;
  padding-top: 20rpx;
  background-color: #fff;
  z-index: 100;
  border-bottom: 1px solid #eee;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

  .my-title {
    position: absolute;
    width: 100%;
    left: 0;
    text-align: center;
    font-size: 34rpx;
    color: rgba(80, 80, 80, 1);
    font-weight: 600;
  }

  .my-return {
    position: absolute;
    left: 0;
    padding: 20rpx 60rpx 0rpx 40rpx;
    z-index: 101;
    /* 确保返回按钮在最上层 */
  }

  .my-return-img {
    width: 24rpx;
    height: 40rpx;
  }
}
</style>
