<template>
  <view class="my-table" v-if="data.length">
    <!-- 表头 -->
    <view class="table-header">
      <view class="table-row">
        <view
          v-for="(column, index) in columns"
          :key="index"
          class="table-cell header-cell"
          :style="{ flex: column.width || 1 }">
          {{ column.title }}
        </view>
      </view>
    </view>

    <!-- 表格内容 -->
    <view class="table-body">
      <view
        v-for="(row, rowIndex) in data"
        :key="rowIndex"
        class="table-row"
        :class="{ 'row-stripe': rowIndex % 2 === 1 }">
        <view
          v-for="(column, colIndex) in columns"
          :key="colIndex"
          class="table-cell"
          :style="{ flex: column.width || 1 }">
          {{ row[column.prop] }}
        </view>
      </view>
    </view>
  </view>
  <NoData :data="data"></NoData>
</template>

<script setup lang="ts">
import NoData from "./noData.vue";
interface Column {
  title: string; // 列标题
  prop: string; // 数据属性名
  width?: number; // 列宽度比例
}

interface Props {
  columns: Column[];
  data: any[];
}

defineProps<Props>();
</script>

<style lang="scss" scoped>
.my-table {
  // width: 120%;
  border: 1rpx solid #ebeef5;
  border-radius: 8rpx;
  overflow: hidden;

  .table-header {
    background-color: #f5f7fa;

    .header-cell {
      font-weight: 500;
      color: #333;
    }
  }

  .table-row {
    display: flex;
    align-items: center;
    border-bottom: 1rpx solid #ebeef5;

    &:last-child {
      border-bottom: none;
    }

    &.row-stripe {
      background-color: #fafafa;
    }

    // &:hover {
    //   background-color: #F5F7FA;
    // }
  }

  .table-cell {
    padding: 24rpx;
    font-size: 28rpx;
    color: #606266;
    text-align: center;
    box-sizing: border-box;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
    border-right: 1rpx solid #ebeef5;

    &:last-child {
      border-right: none;
    }
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .my-table {
    .table-cell {
      padding: 16rpx;
      font-size: 24rpx;
    }
  }
}
</style>
