<template>
  <view class="pageBox">
    <view class="head">
      <view class="return" @click="returnSetp">
        <image
          class="return-img"
          src="../../../../static/icons/fanhui.png"></image>
      </view>
      <text class="title">上传身份资料</text>
    </view>
    <view class="text">为保障账户安全，请填写真实有效的身份信息。</view>
    <view class="uploadBox">
      <view data-name="身份证正面">
        <image src="../../../../static/icons/IDCard-font.png"></image>
      </view>
      <view data-name="身份证反面" style="margin-left: 40rpx">
        <image src="../../../../static/icons/IDCard-back.png"></image>
      </view>
      <view data-name="其它证明材料" style="margin-top: 60rpx">
        <image src="../../../../static/icons/card-other.png"></image>
      </view>
      <view class=""></view>
    </view>
    <view class="formBox">
      <view class="cu-form-group">
        <text class="star hide">*</text>
        <view class="title">补充说明</view>
        <input placeholder="请输入描述" name="input" />
      </view>
      <view class="cu-form-group">
        <text class="star">*</text>
        <view class="title">新手机号</view>
        <input placeholder="输入新手手机号，接收申诉结果" name="input" />
      </view>
      <view class="cu-form-group">
        <text class="star">*</text>
        <view class="title">图片验证码</view>
        <input placeholder="输入验证码" name="input" />
        <text
          class="text-blue verifyCode"
          style="color: var(--blue-2); font-size: var(--size-2)"
          @click="getCode()">
          {{ codeText }}
        </text>
      </view>
      <view class="cu-form-group" style="border: none">
        <button class="submit">确定</button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
// -> 导入-------------------

import { ref } from "vue";
// import toast from '@/components/toast.vue'

// -> 数据-------------------
let codeText = ref<number | string>("获取验证码");
// const ref_toast = ref<InstanceType<typeof toast>>()
// const ref_form = ref<HTMLElement | null>(null)!;
// -> 函数-------------------
const returnSetp = () => {
  uni.navigateBack({
    delta: 1,
  });
};
// 函数
const getCode = () => {
  if (typeof codeText.value == "string") {
    ref_toast.value?.success("发送成功");
    codeText.value = "已发送";
    timerFN(60);
  } else {
    ref_toast.value?.warning("请勿重复获取");
  }
};
const timerFN = (time = 60) => {
  let timer = setInterval(() => {
    time -= 1;
    codeText.value = time;
    if (time <= 0) {
      clearInterval(timer);
      codeText.value = "获取验证码";
    }
  }, 1000);
};
</script>

<style lang="scss" scoped>
.text {
  width: 100% !important;
  padding: 20rpx;
  text-align: center;
  background: #fcfbe9;
}
.uploadBox {
  width: 100%;
  padding: 20rpx 20rpx 60rpx 20px;
  background-color: #f4f4f4;
  display: flex;
  flex-wrap: wrap;
  // justify-content:space-between;
}
.uploadBox > view {
  width: 46%;
  height: 230rpx;
  position: relative;
}
.uploadBox > view::before {
  position: absolute;
  bottom: -60rpx;
  left: 20%;
  content: attr(data-name);
  display: block;
  width: fit-content;
  height: fit-content;
  padding: 20rpx;
  color: #666666;
  font-size: var(--size-1) !important;
}
.uploadBox > view > image {
  width: 100%;
  height: 100%;
  border: 2px dotted #ccc;
  // border-radius: 10px;
}
.formBox {
  width: 100%;
  background-color: white;
  padding: 20rpx;
}
.submit {
  width: 90%;
  margin-left: 5%;
  margin-top: 60rpx;
  height: 82rpx;
  background: linear-gradient(80deg, #677bf0, #2330df);
  box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0, 0, 0, 0.2);
  border-radius: 0rpx 41rpx 41rpx 41rpx;
  color: white;
}
</style>
