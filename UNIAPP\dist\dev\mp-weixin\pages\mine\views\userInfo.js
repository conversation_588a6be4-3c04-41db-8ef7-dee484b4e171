"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const store_user = require("../../../store/user.js");
const utils_http = require("../../../utils/http.js");
const utils_index = require("../../../utils/index.js");
const utils_request = require("../../../utils/request.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
if (!Array) {
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  _easycom_up_button2();
}
const _easycom_up_button = () => "../../../node-modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_up_button + toast)();
}
const toast = () => "../../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "userInfo",
  setup(__props) {
    const store = store_index.useIndexStore();
    const userStore = store_user.useUserStore();
    const ref_toast = common_vendor.ref();
    const whiteList = common_vendor.ref(["Avatar", "Mail", "Password", "Mobile"]);
    const list = common_vendor.ref([
      { name: "头像", value: "", key: "Avatar" },
      { name: "姓名", value: "小明", key: "DisplayName" },
      { name: "邮箱", value: "<EMAIL>", key: "Mail" },
      { name: "手机号", value: "未绑定手机号", key: "Mobile" },
      { name: "修改密码", value: "", key: "Password" },
      { name: "部门", value: "无", key: "DepartmentName" },
      { name: "职位", value: "无", key: "Ex4" }
    ]);
    const OnClicked = (keyName, value) => {
      switch (keyName) {
        case "Mobile":
          common_vendor.index.navigateTo({
            url: "editPhone?data=" + value
          });
          break;
        case "Password":
          common_vendor.index.navigateTo({
            url: "editPassword?data=" + list.value.find((item) => {
              if (item.key === "Mobile") {
                return item.value;
              }
            })
          });
          break;
      }
    };
    const onChooseAvatar = (e) => {
      const url = e.detail.avatarUrl;
      uploadImageAPI(url);
    };
    const onSrc = (value) => {
      console.log("value", value);
      const http = utils_request.getBaseUrl().replace(/\Api\/V1$/, "");
      if (!value) {
        return "../../../static/images/avatar.png";
      } else {
        value = http + value;
        return value;
      }
    };
    const uploadImageAPI = (filePath) => {
      common_vendor.index.showLoading({
        title: "上传中.."
      });
      const http = utils_request.getBaseUrl();
      common_vendor.index.uploadFile({
        url: http + "/User/UpAvatar",
        name: "file",
        fileType: "image",
        filePath,
        //要上传的文件资源
        success: (response) => {
          var _a, _b;
          let res = JSON.parse(response.data);
          console.log("上传结果：", res.Data);
          if (res.Code === 2) {
            (_a = ref_toast.value) == null ? void 0 : _a.info(res.Message);
            list.value[0].value = "";
            return;
          }
          const url = res.Data;
          common_vendor.index.hideLoading();
          (_b = ref_toast.value) == null ? void 0 : _b.success("上传成功");
          console.log("url", url);
          userStore.userInfo.Avatar = url;
          list.value[0].value = url;
        },
        fail(err) {
          var _a;
          console.log("上传失败：", err);
          (_a = ref_toast.value) == null ? void 0 : _a.fail("上传失败！");
        }
      });
    };
    console.log(userStore.userInfo, store.routerUserinfo);
    const onSubmit = async () => {
      var _a, _b;
      const op = {};
      list.value.map((item) => {
        {
          op[item.key] = item.value;
        }
      });
      const res = await utils_http.setUserEmail({ Email: op.Mail });
      const code = res.data.Code;
      if (code != 1) {
        (_a = ref_toast.value) == null ? void 0 : _a.fail(res.data.Message);
      } else {
        (_b = ref_toast.value) == null ? void 0 : _b.success("保存成功");
      }
    };
    const getUserInfoAPI = async () => {
      var _a;
      const res = await utils_http.getUserInfo();
      const data = res.data.Data;
      if (data) {
        userStore.userInfo = data;
        console.log("用户信息", data);
        return;
      } else {
        (_a = ref_toast.value) == null ? void 0 : _a.fail(res.data.Message);
      }
    };
    common_vendor.onShow(async () => {
      await getUserInfoAPI();
      list.value.map((item) => {
        item.value = userStore.userInfo[item.key] ?? "";
      });
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => common_vendor.unref(store).backAPage()),
        b: common_vendor.f(list.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.name),
            b: index == 0
          }, index == 0 ? {
            c: onSrc(item.value),
            d: common_vendor.o(onChooseAvatar, index)
          } : {}, {
            e: item.key === "Mail"
          }, item.key === "Mail" ? {
            f: item.value,
            g: common_vendor.o(($event) => item.value = $event.detail.value, index)
          } : {}, {
            h: !whiteList.value.includes(item.key)
          }, !whiteList.value.includes(item.key) ? {
            i: common_vendor.t(common_vendor.unref(userStore).userInfo[item.key] || "暂无"),
            j: common_vendor.o(($event) => OnClicked(item.key, item.value), index)
          } : {}, {
            k: item.key === "Password"
          }, item.key === "Password" ? {
            l: common_vendor.o(($event) => OnClicked(item.key, item.value), index)
          } : {}, {
            m: item.key === "Mobile"
          }, item.key === "Mobile" ? {
            n: common_vendor.t(common_vendor.unref(utils_index.hidePhoneNumber)(item.value)),
            o: common_vendor.o(($event) => OnClicked(item.key, item.value), index)
          } : {}, {
            p: index
          });
        }),
        c: common_vendor.o(($event) => onSubmit()),
        d: common_vendor.p({
          type: "success"
        }),
        e: common_vendor.sr(ref_toast, "5468c562-1", {
          "k": "ref_toast"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5468c562"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/mine/views/userInfo.vue"]]);
wx.createPage(MiniProgramPage);
