<view class="main data-v-b4c91cf8"><view wx:for="{{a}}" wx:for-item="item" wx:key="n" class="item data-v-b4c91cf8" style="border-bottom:1px solid #E5E5E5"><up-input wx:if="{{item.m}}" u-s="{{['prefix','suffix']}}" class="input data-v-b4c91cf8" u-i="{{item.k}}" bind:__l="__l" bindupdateModelValue="{{item.l}}" u-p="{{item.m}}"><view class="data-v-b4c91cf8" style="border-right:1rpx solid #E5E5E5;padding-right:30rpx;display:flex;place-items:center" slot="prefix"><view class=" data-v-b4c91cf8">{{item.a}}</view><view wx:if="{{item.b}}" class=" data-v-b4c91cf8"></view></view><view wx:if="{{item.c}}" class=" data-v-b4c91cf8" style="margin-right:30rpx" slot="suffix"><up-button wx:if="{{item.j}}" class="data-v-b4c91cf8" u-s="{{['d']}}" bindclick="{{item.h}}" u-i="{{item.i}}" bind:__l="__l" u-p="{{item.j}}"><view class=" data-v-b4c91cf8">{{item.d}}</view><view wx:if="{{item.e}}" class=" data-v-b4c91cf8"><up-icon wx:if="{{item.g}}" class="data-v-b4c91cf8" u-i="{{item.f}}" bind:__l="__l" u-p="{{item.g}}"></up-icon></view></up-button></view></up-input></view><view class=" data-v-b4c91cf8" style="margin-top:10rpx;transition:all .5s"><button class="data-v-b4c91cf8" type="primary" style="width:80%;background:dodgerblue" bindtap="{{b}}">提交</button></view></view><toast class="r data-v-b4c91cf8" u-r="ref_toast" u-i="b4c91cf8-3" bind:__l="__l"></toast>