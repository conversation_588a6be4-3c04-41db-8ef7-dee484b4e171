<template>
  <up-toast ref="uToastRef"></up-toast>
  <!-- toast -->
  <view
    class="cu-modal"
    style="background-color: transparent"
    :class="showModel ? 'show' : ''"
    @tap="hideModal">
    <view class="cu-dialog" :style="modelStyle">
      <view class="cu-bar">
        <view
          class="action margin-0 flex-sub"
          @tap="hideModal"
          style="font-size: 29rpx; line-height: 29rpx">
          <up-icon
            v-if="modelIcon != 'none'"
            :name="modelIcon"
            :color="modelColor"
            size="5.7vw"></up-icon>
          {{ modelText }}
        </view>
      </view>
    </view>
  </view>
  <!-- showModel -->
  <up-modal
    :show="show"
    :title="'提交结果'"
    confirmText="好的"
    @confirm="closeConfirm">
    <view style="display: flex; flex-direction: column">
      <view class="slot-content">
        <image
          src="../static/work/01.png"
          mode="aspectFit"
          style="max-height: 320rpx"></image>
      </view>
      <view
        style="
          text-align: center;
          letter-spacing: 1px;
          color: #666666;
          margin-top: 10rpx;
        ">
        感谢您完成本月度的评测提交
        <text style="color: #ff0079">❤</text>
      </view>
    </view>
  </up-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 实例
const uToastRef = ref<HTMLElement | null>(null)!;
const ref_modelStyle = ref<HTMLElement | null>(null)!;

const show = ref(false);
let showModel = ref(false);
let modelText = ref("朕知道了");
let modelIcon = ref("none");
let modelColor = ref("white");
let modelStyle = ref("background-color: rgba(0, 0, 0, 0.5);color: white;");
let defualtTime = ref(3500);
let hideModal = () => {
  showModel.value = false;
};
const closeConfirm = () => {
  show.value = false;
  setTimeout(() => {
    uni.switchTab({
      url: "../pages/index/index",
    });
  }, 2000);
};

/** 纯描述 - 灰色 */
const info = (text = "执行成功", time = defualtTime.value) => {
  // uToastRef.value?.show({
  // 	type: 'info',
  // 	message: text,
  // 	duration: time
  // })
  uni.showToast({
    title: text,
    duration: time,
    icon: "none",
  });
};
/** 成功 */
const success = (
  text = "操作成功",
  time = defualtTime.value,
  icon = "checkbox-mark"
) => {
  showModel.value = false;
  showModel.value = true;
  modelText.value = text;
  modelIcon.value = icon;
  modelColor.value = "#39b54a";
  modelStyle.value =
    "background:linear-gradient(35deg,#c6f0ce , #90f1bc);color: #35b142;";
  // background-color: #D7F0DB;#c6f0ce
  time === 0 ? (time = 99999) : "";
  setTimeout(() => {
    showModel.value = false;
  }, time);
};
/** 告警 */
const warning = (
  text = "操作告警",
  time = defualtTime.value,
  icon = "arrow-up-fill"
) => {
  showModel.value = false;
  showModel.value = true;
  modelText.value = text;
  modelIcon.value = icon;
  modelStyle.value =
    "background:linear-gradient(-45deg,#f1a265,#ffb472);color: white;letter-spacing: 4rpx;box-shadow:0 0 10px #666666;";
  modelColor.value = "white";
  time === 0 ? (time = 99999) : "";
  //background-color: #f1a265;  #fbbd08
  setTimeout(() => {
    showModel.value = false;
  }, time);
};
/** 失败 */
const fail = (text = "操作失败", time = defualtTime.value, icon = "close") => {
  showModel.value = false;
  showModel.value = true;
  modelText.value = text;
  modelIcon.value = icon;
  modelStyle.value = `background: linear-gradient(160deg, #ff5967 60%, rgb(220, 140, 160));color: #FFFFFF;font-size:16px;box-shadow:0 0 10px #666666;`;
  modelColor.value = "white";
  // #e55b5b
  time === 0 ? (time = 99999) : "";
  setTimeout(() => {
    showModel.value = false;
  }, time);
};
/** 网络失败 */
const netFail = (
  text = "网络连接失败",
  time = defualtTime.value,
  icon = "wifi-off"
) => {
  showModel.value = false;
  showModel.value = true;
  modelText.value = text;
  modelIcon.value = icon;
  modelStyle.value = "background-color: rgba(0,0,0,0.6);color: white;";
  modelColor.value = "red";
  time === 0 ? (time = 99999) : "";
  setTimeout(() => {
    showModel.value = false;
  }, time);
};

defineExpose({
  info,
  success,
  warning,
  fail,
  netFail,
});
</script>

<style lang="scss">
.cu-dialog {
  width: fit-content;
  padding: 0rpx 28rpx 0rpx 0rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  letter-spacing: 2rpx;
}
.cu-modal .cu-dialog > .cu-bar:first-child .action {
  min-height: 2.4rem !important;
  // font-size: 28rpx !important;
}
.cu-bar {
  min-height: 2.8rem !important;
  // border: 1px solid ;
}
.cu-dialog {
  // min-width: 26vw;
  padding: 0px 45rpx 0px 15rpx;
}
.info {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
}
.success {
  background-color: #d7f0db;
  color: #39b54a;
}
.warning {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
}
.fail {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
}

//#FEF4D7
//#FBBD08
//#F37B1D

//#FADBD9
//#E54D42
</style>
