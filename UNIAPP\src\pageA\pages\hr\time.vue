<template>
  <view class="time delHead">
    <Head :title="'评分日期'" returnType="mine"></Head>

    <view class="time-form">
      <view class="tip">
        *请注意日期依次从小到大填写 5{{ "<" }} 8 {{ "<" }} 10 {{ "<" }} 12
      </view>
      <up-form
        labelPosition="top"
        :model="state.formModel"
        :rules="state.rules"
        ref="formRef">
        <up-form-item label="跨部门打分的开始日期:" prop="formData.SubmitDay">
          <up-input
            type="number"
            v-model="state.formModel.formData.SubmitDay"
            clearable
            placeholder="请输入内容" />
        </up-form-item>
        <up-form-item label="跨部门打分的截止日期:" prop="formData.AssignDay">
          <up-input
            type="number"
            v-model="state.formModel.formData.AssignDay"
            clearable
            placeholder="请输入内容" />
        </up-form-item>
        <up-form-item label="催促打分日期:" prop="formData.UrgedDay">
          <up-input
            type="number"
            v-model="state.formModel.formData.UrgedDay"
            clearable
            placeholder="请输入内容" />
        </up-form-item>
        <up-form-item label="停止打分日期:" prop="formData.CompleteDay">
          <up-input
            type="number"
            v-model="state.formModel.formData.CompleteDay"
            clearable
            placeholder="请输入内容" />
        </up-form-item>
        <up-row customStyle="margin: 10px">
          <up-col span="3" />
          <up-col span="6">
            <up-button type="primary" @click="onSubmit" text="提交" />
          </up-col>
        </up-row>
      </up-form>
    </view>
    <toast ref="ref_toast"></toast>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import Head from "@/components/head.vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import toast from "@/components/toast.vue";
import { getDeadline, getUpdateDeadline } from "@/utils/http";
const ref_toast = ref();
const formRef = ref("");
const state = reactive({
  formModel: {
    formData: {
      SubmitDay: "", //提出跨部门打分申请的开始日期5
      AssignDay: "", //主管审核/指定跨部门打分的截止日期8
      UrgedDay: "", //人事催促打分截止日期10
      CompleteDay: "", //停止打分日期12
    },
  },
  rules: {
    "formData.SubmitDay": {
      type: "integer",
      required: true,
      message: "请输入跨部门打分的开始日期",
      trigger: ["blur"],

      asyncValidator: (rule, value, callback) => {
        return onAsyncValidator(rule, value, callback);
      },
    },
    "formData.AssignDay": {
      type: "integer",
      required: true,
      message: "请输入跨部门打分的截止日期",
      trigger: ["blur"],
      asyncValidator: (rule, value, callback) => {
        return onAsyncValidator(rule, value, callback);
      },
    },
    "formData.UrgedDay": {
      type: "integer",
      required: true,
      message: "请输入催促打分日期",
      trigger: ["blur"],
      asyncValidator: (rule, value, callback) => {
        return onAsyncValidator(rule, value, callback);
      },
    },
    "formData.CompleteDay": {
      type: "integer",
      required: true,
      message: "请输入停止打分日期",
      trigger: ["blur"],
      asyncValidator: (rule, value, callback) => {
        return onAsyncValidator(rule, value, callback);
      },
    },
  },
});
onLoad(() => {
  getDeadlineAPI();
});
// const onAsyncValidator = (rule, value, callback) => {
//   console.log(value);
//   const num = parseInt(value);

//   if (isNaN(num) || num <= 0 || num > 15 || num !== Number(value)) {
//     ref_toast.value?.warning("日期可选每月的1-15号");
//     callback(new Error());
//   }

//   callback();
// };
const onAsyncValidator = (rule, value, callback) => {
  // console.log(rule, value, callback);

  const num = parseInt(value);
  // 基本验证：确保是1-15之间的整数
  if (isNaN(num) || num <= 0 || num > 15 || num !== Number(value)) {
    ref_toast.value?.warning("日期可选每月的1-15号");
    callback(new Error());
    return;
  }

  // 验证日期顺序关系
  const formData = state.formModel.formData;
  const fieldName = rule.field;
  // console.log(formData, fieldName);

  if (
    fieldName === "SubmitDay" &&
    parseInt(formData.AssignDay) &&
    parseInt(formData.SubmitDay) >= parseInt(formData.AssignDay)
  ) {
    ref_toast.value?.warning("开始日期必须小于截止日期");
    callback(new Error());
    return;
  }

  if (fieldName === "AssignDay") {
    if (
      parseInt(formData.SubmitDay) &&
      parseInt(formData.AssignDay) <= parseInt(formData.SubmitDay)
    ) {
      ref_toast.value?.warning("截止日期必须大于开始日期");
      callback(new Error());
      return;
    }
    if (
      parseInt(formData.UrgedDay) &&
      parseInt(formData.AssignDay) >= parseInt(formData.UrgedDay)
    ) {
      ref_toast.value?.warning("截止日期必须小于催促日期");
      callback(new Error());
      return;
    }
  }

  if (fieldName === "UrgedDay") {
    if (
      parseInt(formData.AssignDay) &&
      parseInt(formData.UrgedDay) <= parseInt(formData.AssignDay)
    ) {
      ref_toast.value?.warning("催促日期必须大于截止日期");
      callback(new Error());
      return;
    }
    if (
      parseInt(formData.CompleteDay) &&
      parseInt(formData.UrgedDay) >= parseInt(formData.CompleteDay)
    ) {
      ref_toast.value?.warning("催促日期必须小于停止日期");
      callback(new Error());
      return;
    }
  }

  if (
    fieldName === "CompleteDay" &&
    parseInt(formData.UrgedDay) &&
    parseInt(formData.CompleteDay) <= parseInt(formData.UrgedDay)
  ) {
    ref_toast.value?.warning("停止日期必须大于催促日期");
    callback(new Error());
    return;
  }

  callback();
};
const onSubmit = async () => {
  const valid = await formRef.value?.validate();
  if (valid) {
    // console.log(valid);
    getUpDeadline();
  } else {
    ref_toast.value?.warning("请先完善表单！");
  }
};
const getDeadlineAPI = async () => {
  const res = await getDeadline();
  const data = res.data.Data;
  state.formModel.formData = data;
  // console.log("截止日期", data);
};
const getUpDeadline = async () => {
  let res = await getUpdateDeadline(state.formModel.formData);
  // console.log("更新日期", res);
  if (res.data.Code != 1) {
    ref_toast.value?.info(res.data.Message);
  } else {
    ref_toast.value?.success(res.data.Message);
    const data = res.data.Data;
    state.formModel.formData = data;
  }
};
</script>

<style lang="scss" scoped>
.time-form {
  width: 90%;
  margin: 0 auto;
  .tip {
    color: gray;
    font-size: 24rpx;
  }
}
:deep(.time-form .u-form-item__body__left__content__label) {
  width: 400rpx;
}
</style>
