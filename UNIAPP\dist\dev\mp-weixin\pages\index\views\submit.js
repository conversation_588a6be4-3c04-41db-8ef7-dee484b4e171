"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const utils_index = require("../../../utils/index.js");
const store_user = require("../../../store/user.js");
const utils_http = require("../../../utils/http.js");
require("../../../utils/request.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
if (!Array) {
  const _easycom_up_modal2 = common_vendor.resolveComponent("up-modal");
  _easycom_up_modal2();
}
const _easycom_up_modal = () => "../../../node-modules/uview-plus/components/u-modal/u-modal.js";
if (!Math) {
  (toast + _easycom_up_modal)();
}
const toast = () => "../../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "submit",
  setup(__props) {
    var _a, _b, _c;
    const ref_toast = common_vendor.ref();
    const store = store_index.useIndexStore();
    const userStore = store_user.useUserStore();
    const userInfo = common_vendor.ref(store.routerUserinfo);
    const admin = userStore.IsDepartmentlanager;
    const showConfirm = common_vendor.ref(false);
    const modelText1 = common_vendor.ref("感谢您完成本月度的评测提交");
    const tiBig = common_vendor.ref(0);
    const tiSmall = common_vendor.ref(0);
    common_vendor.onLoad((e) => {
      if (e && Object.keys(e).length > 0) {
        tiBig.value = e.tiBig;
        tiSmall.value = e.tiSmall;
      }
    });
    if (store.submitData.TId == 0) {
      userInfo.value = userStore.userInfo;
    } else {
      userInfo.value = store.routerUserinfo;
      console.log(" id --->", store.submitData.BId, (_a = userInfo.value) == null ? void 0 : _a.ID);
      store.submitData.BId = (_b = userInfo.value) == null ? void 0 : _b.ID;
    }
    if (((_c = userInfo.value.Avatar) == null ? void 0 : _c.length) === 0 || !userInfo.value.Avatar) {
      userInfo.value.Avatar = "../../../static/images/avatar.png";
    }
    const formData = common_vendor.ref({
      TId: store.submitData.TId,
      BId: store.submitData.BId,
      Content: JSON.stringify(store.submitData.Content),
      Score: store.submitData.Score,
      Reamrk: "",
      ExtraScore: "",
      DepartmentId: store.routerUserinfo.DepartmentId
    });
    const closeConfirm = () => {
      showConfirm.value = false;
      common_vendor.index.navigateBack({
        delta: 10
      });
    };
    const submit = async function() {
      var _a2, _b2;
      const len = formData.value.Reamrk.length;
      const len2 = formData.value.ExtraScore.length;
      if (len && !len2 && admin) {
        (_a2 = ref_toast.value) == null ? void 0 : _a2.warning("请先评分");
        return;
      }
      common_vendor.index.showLoading({
        title: "数据提交中..."
      });
      if (Number(formData.value.Score) == 0) {
        (_b2 = ref_toast.value) == null ? void 0 : _b2.fail("抱歉，成绩不能为0分");
        return;
      }
      await setAppraisalAPI();
      common_vendor.index.hideLoading();
    };
    const setAppraisalAPI = async () => {
      var _a2;
      const res = await utils_http.setAppraisal(formData.value);
      if (!res)
        return;
      const data = res.data;
      common_vendor.index.hideLoading();
      console.log("提交结果", data);
      if (((_a2 = res.data) == null ? void 0 : _a2.Code) === 2) {
        modelText1.value = res.data.Message;
      }
      showConfirm.value = true;
      return data;
    };
    common_vendor.onShow(() => {
      common_vendor.index.setStorageSync("jianyi", "suibian");
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(($event) => common_vendor.unref(store).backAPage()),
        b: common_vendor.t(userInfo.value.DisplayName != common_vendor.unref(userStore).userInfo.DisplayName ? "被评者" : "姓名"),
        c: common_vendor.t(userInfo.value.DisplayName),
        d: common_vendor.t(userInfo.value.Ex4 || "无"),
        e: common_vendor.t(userInfo.value.Ex4),
        f: common_vendor.t(common_vendor.unref(utils_index.getCurrentDate)()),
        g: common_vendor.t(common_vendor.unref(store).submitData.Score),
        h: common_vendor.unref(utils_index.cantBeBad)(userInfo.value.Avatar) || "../../../static/images/avatar.png",
        i: common_vendor.unref(admin)
      }, common_vendor.unref(admin) ? {} : {}, {
        j: common_vendor.unref(admin)
      }, common_vendor.unref(admin) ? {
        k: formData.value.ExtraScore,
        l: common_vendor.o(($event) => formData.value.ExtraScore = $event.detail.value)
      } : {}, {
        m: formData.value.Reamrk.length,
        n: "/1000",
        o: formData.value.Reamrk,
        p: common_vendor.o(($event) => formData.value.Reamrk = $event.detail.value),
        q: common_vendor.t(tiBig.value),
        r: common_vendor.t(tiSmall.value),
        s: common_vendor.o(($event) => submit()),
        t: common_vendor.sr(ref_toast, "f60df41e-0", {
          "k": "ref_toast"
        }),
        v: modelText1.value === "感谢您完成本月度的评测提交"
      }, modelText1.value === "感谢您完成本月度的评测提交" ? {} : {}, {
        w: common_vendor.t(modelText1.value),
        x: modelText1.value === "感谢您完成本月度的评测提交"
      }, modelText1.value === "感谢您完成本月度的评测提交" ? {} : {}, {
        y: common_vendor.o(closeConfirm),
        z: common_vendor.p({
          show: showConfirm.value,
          title: "提交结果",
          confirmText: modelText1.value === "感谢您完成本月度的评测提交" ? "好的" : " 行 吧 💔"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f60df41e"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/index/views/submit.vue"]]);
wx.createPage(MiniProgramPage);
